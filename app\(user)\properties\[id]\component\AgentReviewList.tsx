'use client';

import { useState, useMemo, useEffect } from 'react';
import {
  useFeedbackList,
  useFeedbackStats,
  useDeleteFeedback,
  useUpdateFeedback,
} from '@/hooks/useFeedback';
import { useUserProfile } from '@/hooks/useUsers';
import { toast } from 'sonner';
import { Star, MoreVertical, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { formatViewedTimeWithHour } from '@/utils/dates/formatViewedTime';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface AgentFeedbackListProps {
  agentId: string;
}

export default function AgentFeedbackList({ agentId }: AgentFeedbackListProps) {
  const [page, setPage] = useState(1);
  const pageSize = 6;
  const [editing, setEditing] = useState<any | null>(null);
  const [editedContent, setEditedContent] = useState('');
  const [editedRating, setEditedRating] = useState(5);
  const [deletingFeedback, setDeletingFeedback] = useState<any | null>(null);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [showLoading, setShowLoading] = useState(true);

  const listParams = useMemo(() => ({ pageNumber: page, pageSize, isDescending: true }), [page]);
  const {
    data: feedbackData,
    isLoading: isLoadingFeedback,
    refetch,
  } = useFeedbackList(agentId, listParams);
  const { data: stats, isLoading: isLoadingStats } = useFeedbackStats(agentId);
  const { data: profile } = useUserProfile();

  const currentUserId = profile?.profile?.userName;

  const { mutate: deleteFeedback } = useDeleteFeedback();
  const { mutate: updateFeedback } = useUpdateFeedback(agentId);

  const feedbackList = feedbackData?.data ?? [];
  const totalPages = feedbackData?.totalPages ?? 1;

  useEffect(() => {
    const handleReload = () => {
      setPage(1);
      refetch();
    };
    window.addEventListener('feedback-submitted', handleReload);
    return () => window.removeEventListener('feedback-submitted', handleReload);
  }, [refetch]);

  const handleEdit = (item: any) => {
    setEditing(item);
    setEditedContent(item.agentFeedback.content);
    setEditedRating(item.agentFeedback.rating);
  };

  const handleUpdate = () => {
    if (!editing) return;
    updateFeedback(
      {
        id: editing.agentFeedback.id,
        content: editedContent,
        rating: editedRating,
      },
      {
        onSuccess: () => {
          // toast.success('Đã cập nhật đánh giá');
          setEditing(null);
          refetch();
        },
      }
    );
  };

  const handleDelete = () => {
    const agentId = deletingFeedback?.agentFeedback?.agentId;
    const fbId = deletingFeedback?.agentFeedback?.id;

    if (!agentId) {
      toast.error('Thiếu ID của agent');
      return;
    }

    deleteFeedback(
      { agentId, id: fbId },
      {
        onSuccess: () => {
          // toast.success('Đã xoá đánh giá');
          refetch();
        },
      }
    );
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  useEffect(() => {
    const timer = setTimeout(() => setShowLoading(false), 10000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoadingFeedback || showLoading) {
    return (
      <section id="feedback" className="space-y-8">
        <div className="space-y-1 mt-6">
          <div className="flex items-center gap-2 text-lg md:text-xl font-semibold">
            <Star className="size-4 md:size-5" />
            Đánh giá của khách hàng
          </div>
        </div>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Đang danh sách đánh giá...</span>
        </div>
      </section>
    );
  }

  return (
    <section id="feedback" className="space-y-8">
      {/* Tổng đánh giá */}
      <div className="space-y-1 mt-6">
        <div className="flex items-center gap-2 text-lg md:text-xl font-semibold">
          <Star className="size-4 md:size-5" />
          Đánh giá của khách hàng
        </div>

        {/* Hiển thị sao và số lượng chỉ khi có feedback */}
        {!isLoadingStats && stats && stats.totalFeedback > 0 && (
          <div className="flex items-center gap-2 text-sm text-gray-700">
            <div className="flex items-center gap-1 text-yellow-500">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.round(stats?.averageRating ?? 0)
                      ? 'fill-yellow-500 text-yellow-500'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="font-medium">
              ({stats?.averageRating?.toFixed(1) ?? '0.0'}) – {stats?.totalFeedback ?? 0} đánh giá
            </span>
          </div>
        )}
      </div>

      {/* Danh sách feedback */}
      {isLoadingFeedback ? (
        <div className="text-center py-6 text-muted-foreground">Đang tải danh sách đánh giá...</div>
      ) : feedbackList.length === 0 ? (
        <div className="text-center text-muted-foreground py-10 text-lg font-semibold">
          Hiện không có đánh giá nào
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {feedbackList.map((item: any) => {
            const fb = item.agentFeedback;
            const username = item.username;

            return (
              <div
                key={fb.id}
                className="bg-gray-50 rounded-lg p-5 space-y-3 relative shadow-sm border border-gray-200 flex flex-col justify-between h-full"
              >
                {/* Nội dung đầu thẻ: tag + rating + content */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="bg-white text-gray-600 text-xs px-2 py-1 rounded-full border border-gray-300 font-semibold">
                      Đánh giá người bán
                    </span>
                    <div className="flex items-center gap-1 text-black font-medium">
                      <Star className="w-4 h-4 fill-black text-black" />
                      <span>{typeof fb.rating === 'number' ? fb.rating.toFixed(1) : 'N/A'}</span>
                    </div>
                  </div>

                  <p className="text-sm text-gray-700 leading-relaxed">{fb.content}</p>
                </div>

                {/* Avatar */}
                <div className="flex justify-between items-center pt-4 mt-auto">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-10 h-10">
                      <AvatarImage
                        src={item.avatar || ''}
                        alt={item.fullname || item.username}
                        className="object-cover"
                      />
                      <AvatarFallback className="bg-red-100 text-red-600 font-semibold text-xs">
                        {getInitials(item.fullname || item.username || 'Ẩn danh')}
                      </AvatarFallback>
                    </Avatar>
                    {/* Time */}
                    <div className="text-xs text-gray-500 leading-tight">
                      <p className="text-sm font-semibold text-gray-800">
                        {item.fullname ?? 'Ẩn danh'}
                      </p>
                      <p>
                        {fb.createdAt
                          ? formatViewedTimeWithHour(fb.createdAt)
                          : 'Không rõ thời gian'}
                      </p>
                    </div>
                  </div>
                  {username === currentUserId && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button size="icon" variant="ghost">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleEdit(item)}>
                          Chỉnh sửa
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            setDeletingFeedback(item);
                            setIsDeleteOpen(true);
                          }}
                        >
                          Xoá
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
      {/* Phân trang */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  className={page === 1 ? 'pointer-events-none text-gray-400' : ''}
                >
                  Trước
                </PaginationPrevious>
              </PaginationItem>

              {(() => {
                const pagesToShow = [];
                if (totalPages <= 5) {
                  for (let i = 1; i <= totalPages; i++) {
                    pagesToShow.push(i);
                  }
                } else {
                  pagesToShow.push(1);
                  if (page > 3) pagesToShow.push('...');
                  const middlePages = [page - 1, page, page + 1].filter(
                    p => p > 1 && p < totalPages
                  );
                  pagesToShow.push(...middlePages);
                  if (page < totalPages - 2) pagesToShow.push('...');
                  pagesToShow.push(totalPages);
                }

                return pagesToShow.map((p, i) =>
                  p === '...' ? (
                    <PaginationItem key={`ellipsis-${i}`}>
                      <span className="px-2 py-1 text-sm text-gray-400">...</span>
                    </PaginationItem>
                  ) : (
                    <PaginationItem key={p}>
                      <Button
                        variant={p === page ? 'outline' : 'ghost'}
                        className={`px-3 py-1 text-sm ${p === page ? 'border border-gray-300' : ''}`}
                        onClick={() => setPage(p as number)}
                      >
                        {p}
                      </Button>
                    </PaginationItem>
                  )
                );
              })()}

              <PaginationItem>
                <PaginationNext
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  className={page === totalPages ? 'pointer-events-none text-gray-400' : ''}
                >
                  Tiếp
                </PaginationNext>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* Dialog sửa */}
      <Dialog open={!!editing} onOpenChange={() => setEditing(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Chỉnh sửa đánh giá</DialogTitle>
            <p className="text-sm text-gray-500 mt-1">Hãy chia sẻ cảm nhận của bạn</p>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="flex items-center justify-center gap-1">
              {[1, 2, 3, 4, 5].map(i => (
                <Star
                  key={i}
                  className={`w-6 h-6 cursor-pointer ${i <= editedRating ? 'text-yellow-400' : 'text-gray-300'}`}
                  onClick={() => setEditedRating(i)}
                  fill={i <= editedRating ? 'currentColor' : 'none'}
                />
              ))}
            </div>
            <Textarea
              rows={4}
              value={editedContent}
              onChange={e => setEditedContent(e.target.value)}
              placeholder="Cập nhật nội dung đánh giá..."
            />
          </div>
          <DialogFooter>
            <Button className="w-full" onClick={handleUpdate}>
              Cập nhật đánh giá
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog xoá */}
      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bạn chắc chắn muốn xoá đánh giá này?</DialogTitle>
          </DialogHeader>
          <DialogFooter>
            <Button variant="ghost" onClick={() => setIsDeleteOpen(false)}>
              Huỷ
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                const id = deletingFeedback?.agentFeedback?.id;
                if (!id) {
                  toast.error('Thiếu ID đánh giá');
                  return;
                }
                handleDelete();
                setDeletingFeedback(null);
                setIsDeleteOpen(false);
              }}
            >
              Xoá
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </section>
  );
}
