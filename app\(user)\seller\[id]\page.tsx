import { Metadata } from 'next';
import SellerProfileClient from './components/SellerProfileClient';

export interface SellerProfilePageProps {
  params: { id: string };
}

export async function generateMetadata({
  params: _params,
}: SellerProfilePageProps): Promise<Metadata> {
  // TODO: Fetch seller data for metadata
  return {
    title: 'Thông tin môi giới - RevoLand',
    description: 'Xem thông tin chi tiết và danh sách bất động sản của môi giới',
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function SellerProfilePage({ params }: SellerProfilePageProps) {
  return <SellerProfileClient sellerId={params.id} />;
}
