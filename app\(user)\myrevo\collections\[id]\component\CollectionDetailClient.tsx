'use client';

import { useRouter } from 'next/navigation';
import { useCollections } from '@/hooks/useCollections';
import CollectionDetailView from '../../../components/CollectionDetailView';
import { notFound } from 'next/navigation';
import Loading from '../loading';

interface CollectionDetailClientProps {
  collectionId: string;
}

export default function CollectionDetailClient({ collectionId }: CollectionDetailClientProps) {
  const router = useRouter();
  //   const { data: collection, isLoading, error } = useCollection(collectionId);
  const { useCollectionWithProperties } = useCollections();

  const {
    data: collectionWithProperties,
    isLoading: isLoadingProperties,
    error: propertiesError,
  } = useCollectionWithProperties(collectionId);
  const handleBack = () => {
    // Try to go back in history, otherwise navigate to myrevo page
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/myrevo');
    }
  };

  if (isLoadingProperties) {
    return <Loading />;
  }

  if (propertiesError || !collectionWithProperties) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-4 md:pt-8">
      <CollectionDetailView collection={collectionWithProperties} onBack={handleBack} />
    </div>
  );
}
