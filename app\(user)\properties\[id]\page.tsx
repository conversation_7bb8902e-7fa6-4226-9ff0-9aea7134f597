import { Metadata } from 'next';
import { propertyService, Property, PropertyType } from '@/lib/api/services/fetchProperty';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import PropertyDetailClient from './component/PropertyDetailClient';
import { TooltipProvider } from '@/components/ui/tooltip';

export interface PropertyDetailPageProps {
  params: { id: string };
}

export async function generateMetadata({ params }: PropertyDetailPageProps): Promise<Metadata> {
  try {
    const response = await propertyService.getProperty(params.id);
    const property = response.data;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://revoland.com';

    if (!property) {
      return {
        title: 'Bất động sản không tồn tại - RevoLand',
        description: 'Không tìm thấy bất động sản này',
        robots: {
          index: false,
          follow: false,
        },
      };
    }

    // Construct the property URL
    const propertyUrl = `${baseUrl}/properties/${property.id}`;

    // Generate price information
    // const priceInfo = generatePriceInfo(property);
    // const locationInfo = generateLocationInfo(property);
    // const propertyTypeInfo = generatePropertyTypeInfo(property);

    // Create SEO-friendly title and description
    const seoTitle = generateSEOTitle(property);
    const seoDescription = generateSEODescription(property);

    // Get the first image for Open Graph
    const mainImage = property.imageUrls?.[0] || '/hero.jpg';
    const ogImages = property.imageUrls?.slice(0, 4) || [];

    // Build URL manually without encoding to avoid issues with Satori
    const imageParams = ogImages.map((img, idx) => `img${idx + 1}=${img}`).join('&');
    const ogImageUrl = imageParams ? `${baseUrl}/api/og?${imageParams}` : mainImage;

    return {
      title: seoTitle,
      description: seoDescription,
      openGraph: {
        type: 'website',
        title: seoTitle,
        description: seoDescription,
        images: [
          {
            url: ogImageUrl,
            width: 1200,
            height: 630,
            alt: property.title,
          },
        ],
        siteName: 'RevoLand',
        locale: 'vi_VN',
        url: propertyUrl,
      },
      twitter: {
        card: 'summary_large_image',
        title: seoTitle,
        description: seoDescription,
        images: [ogImageUrl],
        site: '@RevoLand',
      },
      alternates: {
        canonical: propertyUrl,
      },
      keywords: [
        property.title,
        property.type,
        property.transactionType,
        property.location.city,
        property.location.district,
        'bất động sản',
        'mua bán nhà đất',
        'cho thuê bất động sản',
        'RevoLand',
        ...generateKeywords(property),
      ],
      creator: 'RevoLand',
      publisher: 'RevoLand',
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      other: {
        'property:price:amount':
          property.priceDetails.salePrice?.toString() ||
          property.priceDetails.rentalPrice?.toString() ||
          '',
        'property:price:currency': property.priceDetails.currency,
        'property:location:latitude': property.location.latitude.toString(),
        'property:location:longitude': property.location.longitude.toString(),
        'property:type': property.type,
        'property:transaction_type': property.transactionType,
        'property:bedrooms': property.propertyDetails.bedrooms?.toString() || '',
        'property:bathrooms': property.propertyDetails.bathrooms?.toString() || '',
        'property:land_area': property.propertyDetails.landArea?.toString() || '',
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Bất động sản - RevoLand',
      description: 'Khám phá các bất động sản chất lượng tại RevoLand',
    };
  }
}

// function generatePriceInfo(property: Property): string {
//   const { priceDetails } = property;

//   if (property.transactionType === 'ForSale' && priceDetails.salePrice) {
//     return `Giá bán: ${formatCurrency(priceDetails.salePrice, priceDetails.currency)}`;
//   } else if (property.transactionType === 'ForRent' && priceDetails.rentalPrice) {
//     return `Giá thuê: ${formatCurrency(priceDetails.rentalPrice, priceDetails.currency)}/tháng`;
//   } else if (priceDetails.pricePerSquareMeter) {
//     return `Giá: ${formatCurrency(priceDetails.pricePerSquareMeter, priceDetails.currency)}/m²`;
//   }

//   return 'Liên hệ để biết giá';
// }

// function generateLocationInfo(property: Property): string {
//   const { location } = property;
//   const parts = [location.ward, location.district, location.city].filter(Boolean);
//   return parts.join(', ');
// }

// function generatePropertyTypeInfo(property: Property): string {
//   const { type, propertyDetails } = property;

//   let info = `Loại: ${type}`;

//   if (propertyDetails.bedrooms) {
//     info += `, ${propertyDetails.bedrooms} phòng ngủ`;
//   }
//   if (propertyDetails.bathrooms) {
//     info += `, ${propertyDetails.bathrooms} phòng tắm`;
//   }
//   if (propertyDetails.landArea) {
//     info += `, ${propertyDetails.landArea}m²`;
//   }

//   return info;
// }

function generateKeywords(property: Property): string[] {
  const keywords: string[] = [];

  // Add property type keywords
  keywords.push(property.type);

  // Add transaction type keywords
  if (property.transactionType === 'ForSale') {
    keywords.push('mua bán', 'bán nhà', 'bán đất');
  } else if (property.transactionType === 'ForRent') {
    keywords.push('cho thuê', 'thuê nhà', 'thuê đất');
  }

  // Add location keywords
  keywords.push(property.location.city, property.location.district, property.location.ward);

  // Add property details keywords
  if (property.propertyDetails.bedrooms) {
    keywords.push(`${property.propertyDetails.bedrooms} phòng ngủ`);
  }
  if (property.propertyDetails.bathrooms) {
    keywords.push(`${property.propertyDetails.bathrooms} phòng tắm`);
  }
  if (property.propertyDetails.landArea) {
    keywords.push(`${property.propertyDetails.landArea}m²`);
  }

  // Add amenities keywords
  const amenities = property.amenities;
  if (amenities.parking) keywords.push('có chỗ đậu xe');
  if (amenities.elevator) keywords.push('có thang máy');
  if (amenities.swimmingPool) keywords.push('có hồ bơi');
  if (amenities.gym) keywords.push('có phòng gym');
  if (amenities.securitySystem) keywords.push('có bảo vệ');
  if (amenities.airConditioning) keywords.push('có điều hòa');
  if (amenities.balcony) keywords.push('có ban công');
  if (amenities.garden) keywords.push('có vườn');

  return keywords;
}

function generateSEOTitle(property: Property): string {
  const parts: string[] = [];

  // 1. Transaction type first
  const transactionText =
    property.transactionType === 'ForSale'
      ? 'Bán'
      : property.transactionType === 'ForRent'
        ? 'Cho thuê'
        : 'Dự án';
  parts.push(transactionText);

  // 2. Property type (Vietnamese translation)
  const propertyTypeText = getVietnamesePropertyType(property.type);
  parts.push(propertyTypeText);

  // 3. Location (most specific first)
  const locationParts = [
    property.location.ward,
    property.location.district,
    property.location.city,
  ].filter(Boolean);
  if (locationParts.length > 0) {
    parts.push(`tại ${locationParts.join(', ')}`);
  }

  // 4. Price (concise format)
  const priceText = getFormattedPrice(property);
  if (priceText) {
    parts.push(priceText);
  }

  // 5. Room information (for applicable property types)
  const roomInfo = getRoomInformation(property);
  if (roomInfo) {
    parts.push(roomInfo);
  }

  // 6. Area information
  const areaInfo = getAreaInformation(property);
  if (areaInfo) {
    parts.push(areaInfo);
  }

  // 7. End with Revoland
  const title = parts.join(' ') + ' | Revoland';

  // Ensure title doesn't exceed recommended length (60 characters for optimal SEO)
  return title.length > 60 ? `${title.substring(0, 57)}...` : title;
}

function generateSEODescription(property: Property): string {
  const parts: string[] = [];

  // Start with transaction and property type
  const transactionText =
    property.transactionType === 'ForSale'
      ? 'Bán'
      : property.transactionType === 'ForRent'
        ? 'Cho thuê'
        : 'Dự án';
  const propertyTypeText = getVietnamesePropertyType(property.type);

  parts.push(`${transactionText} ${propertyTypeText.toLowerCase()}`);

  // Add detailed location
  const locationParts = [
    property.location.ward,
    property.location.district,
    property.location.city,
  ].filter(Boolean);
  if (locationParts.length > 0) {
    parts.push(`tại ${locationParts.join(', ')}`);
  }

  // Add comprehensive property details
  const details: string[] = [];

  // Room information
  const roomInfo = getRoomInformation(property);
  if (roomInfo) {
    details.push(roomInfo);
  }

  // Area information
  const areaInfo = getAreaInformation(property);
  if (areaInfo) {
    details.push(areaInfo);
  }

  // Price information
  const priceText = getFormattedPrice(property);
  if (priceText) {
    details.push(priceText);
  }

  // Add details to description
  if (details.length > 0) {
    parts.push(`- ${details.join(', ')}`);
  }

  // Add amenities if available
  const amenityText = getAmenityHighlights(property);
  if (amenityText) {
    parts.push(amenityText);
  }

  // Add custom description if available (truncated)
  if (property.description && property.description.trim()) {
    const cleanDescription = property.description.trim();
    const truncatedDescription =
      cleanDescription.length > 50 ? `${cleanDescription.substring(0, 50)}...` : cleanDescription;
    parts.push(truncatedDescription);
  }

  // Add contact information
  parts.push('Liên hệ ngay để xem nhà và nhận tư vấn miễn phí.');

  const description = parts.join(' ');

  // Ensure description doesn't exceed recommended length (160 characters for optimal SEO)
  return description.length > 160 ? `${description.substring(0, 157)}...` : description;
}

function getVietnamesePropertyType(type: PropertyType): string {
  const typeMap: Record<PropertyType, string> = {
    [PropertyType.APARTMENT]: 'Căn hộ',
    [PropertyType.MINI_SERVICE_APARTMENT]: 'Căn hộ dịch vụ mini',
    [PropertyType.COMMERCIAL_TOWNHOUSE]: 'Nhà phố thương mại',
    [PropertyType.MOTEL]: 'Nhà trọ',
    [PropertyType.AIRBNB]: 'Airbnb',
    [PropertyType.HOUSE]: 'Nhà riêng',
    [PropertyType.TOWNHOUSE]: 'Nhà phố',
    [PropertyType.VILLA]: 'Biệt thự',
    [PropertyType.SHOP_HOUSE]: 'Shophouse',
    [PropertyType.LAND_PLOT]: 'Đất nền',
    [PropertyType.PROJECT_LAND]: 'Đất dự án',
    [PropertyType.OFFICE]: 'Văn phòng',
    [PropertyType.WAREHOUSE]: 'Kho xưởng',
    [PropertyType.FACTORY]: 'Nhà máy',
    [PropertyType.INDUSTRIAL]: 'Khu công nghiệp',
    [PropertyType.HOTEL]: 'Khách sạn',
    [PropertyType.SOCIAL_HOUSING]: 'Nhà ở xã hội',
    [PropertyType.NEW_URBAN_AREA]: 'Khu đô thị mới',
    [PropertyType.ECO_RESORT]: 'Eco resort',
    [PropertyType.OTHER]: 'Bất động sản khác',
  };

  return typeMap[type] || 'Bất động sản';
}

function getFormattedPrice(property: Property): string | null {
  const { priceDetails, transactionType } = property;

  if (transactionType === 'ForSale' && priceDetails.salePrice) {
    return formatCurrency(priceDetails.salePrice, priceDetails.currency);
  } else if (transactionType === 'ForRent' && priceDetails.rentalPrice) {
    return `${formatCurrency(priceDetails.rentalPrice, priceDetails.currency)}/tháng`;
  } else if (priceDetails.pricePerSquareMeter) {
    return `${formatCurrency(priceDetails.pricePerSquareMeter, priceDetails.currency)}/m²`;
  }

  return null;
}

function getRoomInformation(property: Property): string | null {
  const { propertyDetails, type } = property;

  // For land plots, don't show room information
  if (type === PropertyType.LAND_PLOT || type === PropertyType.PROJECT_LAND) {
    return null;
  }

  const rooms: string[] = [];

  if (propertyDetails.bedrooms !== undefined) {
    if (propertyDetails.bedrooms === 0) {
      rooms.push('Studio');
    } else {
      rooms.push(`${propertyDetails.bedrooms} PN`);
    }
  }

  if (propertyDetails.bathrooms) {
    rooms.push(`${propertyDetails.bathrooms} WC`);
  }

  return rooms.length > 0 ? rooms.join(', ') : null;
}

function getAreaInformation(property: Property): string | null {
  const { propertyDetails } = property;
  const areas: string[] = [];

  if (propertyDetails.landArea) {
    areas.push(`${propertyDetails.landArea}m² đất`);
  }

  if (propertyDetails.buildingArea) {
    areas.push(`${propertyDetails.buildingArea}m² xây dựng`);
  }

  return areas.length > 0 ? areas.join(', ') : null;
}

function getAmenityHighlights(property: Property): string | null {
  const { amenities } = property;
  const highlights: string[] = [];

  if (amenities.swimmingPool) highlights.push('hồ bơi');
  if (amenities.gym) highlights.push('phòng gym');
  if (amenities.parking) highlights.push('chỗ đậu xe');
  if (amenities.securitySystem) highlights.push('bảo vệ 24/7');
  if (amenities.elevator) highlights.push('thang máy');
  if (amenities.airConditioning) highlights.push('điều hòa');
  if (amenities.balcony) highlights.push('ban công');
  if (amenities.garden) highlights.push('vườn');

  return highlights.length > 0 ? `Tiện ích: ${highlights.join(', ')}.` : null;
}

export default async function PropertyDetailPage({ params }: PropertyDetailPageProps) {
  return (
    <TooltipProvider>
      <PropertyDetailClient propertyId={params.id} />
    </TooltipProvider>
  );
}
