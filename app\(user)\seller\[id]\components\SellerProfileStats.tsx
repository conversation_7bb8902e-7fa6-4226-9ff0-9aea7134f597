'use client';

interface StatsData {
  totalSales: number;
  totalListings: number;
  averageDaysOnMarket: number;
  clientSatisfaction: number;
  joinedAt?: string;
  yearsOfExperience?: number | null;
}

interface SellerProfileStatsProps {
  stats: StatsData;
}

function SellerProfileStats({ stats }: SellerProfileStatsProps) {
  // Format joined date to get year
  const getJoinedYear = (dateString?: string) => {
    if (!dateString) return new Date().getFullYear();
    return new Date(dateString).getFullYear();
  };

  const statsItems = [
    {
      year: getJoinedYear(stats.joinedAt),
      title: 'Thành Viên Từ',
      subtitle: `Tham gia nền tảng từ năm ${getJoinedYear(stats.joinedAt)}`,
    },
    {
      year: stats.yearsOfExperience || 'N/A',
      title: '<PERSON><PERSON><PERSON>',
      subtitle: stats.yearsOfExperience
        ? `${stats.yearsOfExperience} năm kinh nghiệm trong lĩnh vực`
        : '<PERSON>ưa cập nhật thông tin kinh nghiệm',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Single container with left-right layout */}
      <div className="bg-gray-100 rounded-lg p-6 md:p-8">
        <div className="flex flex-col md:flex-row md:divide-x md:divide-gray-300">
          {statsItems.map((item, index) => (
            <div
              key={index}
              className="flex-1 px-0 md:px-8 py-4 md:py-0 first:md:pl-0 last:md:pr-0"
            >
              <div className="space-y-2">
                {/* Year - Large and bold */}
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">{item.year}</h2>

                {/* Title */}
                <h3 className="text-base md:text-lg font-semibold text-gray-800">{item.title}</h3>

                {/* Subtitle */}
                <p className="text-sm md:text-base text-gray-600">{item.subtitle}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default SellerProfileStats;
