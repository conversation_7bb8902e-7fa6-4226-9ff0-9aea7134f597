import apiService from '../core';

export interface User {
  userName: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  avatar?: string;
  status?: string;
  role: string;
  about?: string;
  birthDate: string;
  joinedAt: string;
}

export interface Seller {
  id: string;
  userName: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  avatar: string;
  status: string;
  role: string;
  about: string;
  gender: string;
  birthdate: string;
  joinedAt: string;
  isVerified: boolean;
}

export interface UserResponse {
  code: string;
  status: boolean;
  message?: string;
  data: User;
}

export interface UserUpdateResponse {
  code: string;
  status: boolean;
  message?: string;
  data?: string;
}

export interface GetSellerResponse {
  code: number;
  status: boolean;
  message?: string;
  data: {
    data: Seller[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  };
}

export interface AgentFeedback {
  id: string;
  clientName: string;
  rating: number;
  date: string;
  comment: string;
}

export interface SellerProperty {
  propertyId: string;
  title: string;
  images: string;
  transactionType: string;
  price: number;
  bedrooms: number;
  bathrooms: number;
  landArea: number;
  buildingArea: number;
  address: string;
}

export interface SellerProfile {
  sellerId: string;
  name: string;
  phone: string | null;
  avatar: string | null;
  coverPhoto: string | null;
  introduction: string | null;
  joinedAt: string;
  yearsOfExperience: number | null;
  companyName: string | null;
  agentFeedbacks: AgentFeedback[];
  serviceLocations: string | null;
  fieldOfWorks: string | null;
  properties: SellerProperty[];
}

export interface SellerProfileResponse {
  code: number;
  status: boolean;
  message: string;
  data: SellerProfile;
}

// User service with profile-related API methods
export const userService = {
  // Get current user profile
  getUserProfile: async (): Promise<UserResponse> => {
    const response = await apiService.get<UserResponse>('/api/users/profile');
    return response.data;
  },

  // Update current user profile
  updateUserProfile: async (profileData: Partial<User> | FormData): Promise<UserUpdateResponse> => {
    // The API service already handles FormData appropriately in its interceptors
    const response = await apiService.put<UserUpdateResponse, Partial<User> | FormData>(
      '/api/users/update-profile',
      profileData
    );
    return response.data;
  },

  // Get seller profile
  getSellerProfile: async (): Promise<GetSellerResponse> => {
    const response = await apiService.get<GetSellerResponse>('/api/users/seller');
    return response.data;
  },

  // Get seller profile by ID
  getSellerProfileById: async (sellerId: string): Promise<SellerProfileResponse> => {
    const response = await apiService.get<SellerProfileResponse>(
      `/api/users/seller-profile/${sellerId}`
    );
    return response.data;
  },
};

export default userService;
