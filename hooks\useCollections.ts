import { useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  Collection,
  CreateCollectionRequest,
  AddToCollectionRequest,
  RemoveFromCollectionRequest,
  UpdateCollectionRequest,
  collectionService,
  ApiCollectionData,
  ApiCollectionsResponse,
} from '@/lib/api/services/fetchCollection';
import { useAuthStore } from '@/lib/store/authStore';

export function useCollections() {
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const queryClient = useQueryClient();

  // Get all collections with optimized image loading and rate limiting
  const {
    data: collectionsResponse,
    isLoading,
    error,
    refetch,
    isFetching,
  } = useQuery({
    queryKey: ['collections'],
    queryFn: async () => {
      try {
        // Import apiService dynamically to avoid circular dependency
        const { default: apiService } = await import('@/lib/api/core');
        const response = await apiService.get<ApiCollectionsResponse>(
          '/api/CollectionProperty/GetAllByUser'
        );

        // Handle empty collections case - don't treat as error
        if (
          !response.data.status &&
          (response.data.code === 500 || response.data.code === 400) &&
          response.data.message?.includes('Không tìm thấy Collection nào cho người dùng')
        ) {
          return {
            code: 200,
            status: true,
            message: 'No collections found',
            data: [],
            rawApiData: { code: 200, status: true, message: 'No collections found', data: [] },
          };
        }

        if (!response.data.status) {
          throw new Error(response.data.message || 'Không thể tải bộ sưu tập');
        }

        // Process collections with optimized image handling
        const collections = (response.data.data || [])
          .slice(0, 10) // Limit to prevent overload
          .map((apiData: ApiCollectionData) => {
            const collectionImages = (apiData.collectionImage || [])
              .filter(img => img && img.trim() !== '' && img.includes('storage.googleapis.com'))
              .slice(0, 4);

            return {
              id: apiData.collectionId,
              name: apiData.collectionName,
              description: apiData.description || undefined,
              userId: apiData.userId,
              itemCount: apiData.listProperties?.length || 0,
              thumbnailUrl: collectionImages[0] || undefined,
              collectionImage: collectionImages,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              isImageLoaded: false,
            };
          });

        return {
          code: response.data.code,
          status: response.data.status,
          message: response.data.message,
          data: collections,
          rawApiData: response.data,
        };
      } catch (error: any) {
        // Check if this is the "no collections found" error
        if (error.message?.includes('Không tìm thấy Collection nào cho người dùng')) {
          return {
            code: 200,
            status: true,
            message: 'No collections found',
            data: [],
            rawApiData: { code: 200, status: true, message: 'No collections found', data: [] },
          };
        }

        // Re-throw other errors
        throw error;
      }
    },
    enabled: isAuthenticated,
    retry: (failureCount, error) => {
      // Don't retry if it's a "no collections found" error
      if (error?.message?.includes('Không tìm thấy Collection nào cho người dùng')) {
        return false;
      }
      return failureCount < 1;
    },
    retryDelay: attemptIndex => Math.min(3000 * 2 ** attemptIndex, 15000),
    staleTime: 15 * 60 * 1000,
    gcTime: 45 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    refetchOnMount: false,
  });

  const collections = collectionsResponse?.data || [];
  const rawCollectionsData = collectionsResponse?.rawApiData;

  // Create new collection with debouncing
  const createCollectionMutation = useMutation({
    mutationFn: async (request: CreateCollectionRequest): Promise<void> => {
      const response = await collectionService.createCollection(request);
      if (!response.status) {
        throw new Error(response.message || 'Tạo bộ sưu tập thất bại');
      }
    },
    onSuccess: () => {
      toast.success('Tạo bộ sưu tập thành công', {
        style: {
          backgroundColor: 'white',
          color: 'black',
        },
      });
      // Delay invalidation to prevent rapid requests
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['collections'] });
        queryClient.invalidateQueries({ queryKey: ['collections-raw'] });
      }, 2000);
    },
    onError: error => {
      toast.error(error instanceof Error ? error.message : 'Tạo bộ sưu tập thất bại');
    },
  });

  // Add property to collection with debouncing
  const addToCollectionMutation = useMutation({
    mutationFn: async (request: AddToCollectionRequest): Promise<void> => {
      const response = await collectionService.addPropertyToCollection(request);
      if (!response.status) {
        throw new Error(response.message || 'Thêm vào bộ sưu tập thất bại');
      }
    },
    onSuccess: () => {
      toast.success('Đã thêm vào bộ sưu tập', {
        style: {
          backgroundColor: 'white',
          color: 'black',
        },
      });
      // Delay invalidation to prevent rapid requests
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['collections'] });
        queryClient.invalidateQueries({ queryKey: ['collections-raw'] });
        queryClient.invalidateQueries({ queryKey: ['collection-with-properties'] });
      }, 2000);
    },
    onError: error => {
      toast.error(error instanceof Error ? error.message : 'Thêm vào bộ sưu tập thất bại');
    },
  });

  // Remove from collection
  const removeFromCollectionMutation = useMutation({
    mutationFn: async (request: RemoveFromCollectionRequest): Promise<void> => {
      const response = await collectionService.removePropertyFromCollection(request);
      if (!response.status) {
        throw new Error(response.message || 'Xóa khỏi bộ sưu tập thất bại');
      }
    },
    onSuccess: () => {
      toast.success('Đã xóa khỏi bộ sưu tập');
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['collections'] });
        queryClient.invalidateQueries({ queryKey: ['collections-raw'] });
        queryClient.invalidateQueries({ queryKey: ['collection-with-properties'] });
      }, 2000);
    },
    onError: error => {
      toast.error(error instanceof Error ? error.message : 'Xóa khỏi bộ sưu tập thất bại');
    },
  });

  // Remove property from ALL collections - new function for favorite button
  const removeFromAllCollectionsMutation = useMutation({
    mutationFn: async (propertyId: string): Promise<void> => {
      // Get collections containing this property
      const collectionsWithProperty = getCollectionsForProperty(propertyId);

      if (collectionsWithProperty.length === 0) {
        throw new Error('Property không có trong bộ sưu tập nào');
      }

      // Remove from all collections sequentially
      for (const collection of collectionsWithProperty) {
        const response = await collectionService.removePropertyFromCollection({
          collectionId: collection.id,
          propertyId,
        });

        if (!response.status) {
          throw new Error(response.message || `Xóa khỏi bộ sưu tập ${collection.name} thất bại`);
        }
      }
    },
    onSuccess: () => {
      toast.success('Đã xóa khỏi tất cả bộ sưu tập');
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['collections'] });
        queryClient.invalidateQueries({ queryKey: ['collections-raw'] });
        queryClient.invalidateQueries({ queryKey: ['collection-with-properties'] });
      }, 2000);
    },
    onError: error => {
      toast.error(error instanceof Error ? error.message : 'Xóa khỏi bộ sưu tập thất bại');
    },
  });

  // Delete collection
  const deleteCollectionMutation = useMutation({
    mutationFn: async (collectionId: string): Promise<void> => {
      const response = await collectionService.deleteCollection(collectionId);
      if (!response.status) {
        throw new Error(response.message || 'Xóa bộ sưu tập thất bại');
      }
    },
    onSuccess: () => {
      toast.success('Đã xóa bộ sưu tập');
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['collections'] });
        queryClient.invalidateQueries({ queryKey: ['collections-raw'] });
      }, 2000);
    },
    onError: error => {
      toast.error(error instanceof Error ? error.message : 'Xóa bộ sưu tập thất bại');
    },
  });

  // Update collection
  const updateCollectionMutation = useMutation({
    mutationFn: async ({
      collectionId,
      data,
    }: {
      collectionId: string;
      data: UpdateCollectionRequest;
    }): Promise<void> => {
      const response = await collectionService.updateCollection(collectionId, data);
      if (!response.status) {
        throw new Error(response.message || 'Cập nhật bộ sưu tập thất bại');
      }
    },
    onSuccess: () => {
      toast.success('Cập nhật bộ sưu tập thành công');
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['collections'] });
        queryClient.invalidateQueries({ queryKey: ['collections-raw'] });
      }, 2000);
    },
    onError: error => {
      toast.error(error instanceof Error ? error.message : 'Cập nhật bộ sưu tập thất bại');
    },
  });

  // Get collection items (from API data) - placeholder for future use
  const getCollectionItems = useCallback((_collectionId: string) => {
    // TODO: Implement when needed
    return [];
  }, []);

  // Check if property is in any collection - now implemented
  const isPropertyInCollections = useCallback(
    (propertyId: string): boolean => {
      if (!rawCollectionsData?.data || !isAuthenticated) return false;

      // Check if property exists in any collection's listProperties
      return rawCollectionsData.data.some((apiCollection: ApiCollectionData) =>
        apiCollection.listProperties?.some(property => property.id === propertyId)
      );
    },
    [rawCollectionsData, isAuthenticated]
  );

  // Get collections containing a specific property - now implemented
  const getCollectionsForProperty = useCallback(
    (propertyId: string): Collection[] => {
      if (!rawCollectionsData?.data || !isAuthenticated) return [];

      const result: Collection[] = [];

      rawCollectionsData.data.forEach((apiCollection: ApiCollectionData) => {
        if (apiCollection.listProperties?.some(property => property.id === propertyId)) {
          // Transform API data to Collection format
          result.push({
            id: apiCollection.collectionId,
            name: apiCollection.collectionName,
            description: apiCollection.description || undefined,
            userId: apiCollection.userId,
            itemCount: apiCollection.listProperties?.length || 0,
            thumbnailUrl: apiCollection.collectionImage?.[0] || undefined,
            collectionImage: apiCollection.collectionImage || [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isImageLoaded: false,
          });
        }
      });

      return result;
    },
    [rawCollectionsData, isAuthenticated]
  );

  // Get collection with properties - optimized with lazy loading
  const useCollectionWithProperties = (collectionId: string) => {
    return useQuery({
      queryKey: ['collection-with-properties', collectionId],
      queryFn: async () => {
        // Use the new specific collection API instead of fetching all collections
        const result = await collectionService.getCollectionWithPropertiesById(collectionId);
        if (!result) {
          throw new Error('Không thể tải bộ sưu tập');
        }
        return result;
      },
      enabled: !!collectionId,
      retry: 1, // Reduce retry for detail views
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 15 * 60 * 1000, // 15 minutes
      refetchOnWindowFocus: false,
    });
  };

  // Preload images for visible collections (lazy loading)
  const preloadCollectionImages = useCallback((visibleCollections: Collection[]) => {
    visibleCollections.forEach(collection => {
      if (collection.collectionImage && collection.collectionImage.length > 0) {
        // Preload only the first image to reduce server load
        const img = new Image();
        img.src = collection.collectionImage[0];
        img.loading = 'lazy';
      }
    });
  }, []);

  return {
    // Data
    collections,
    isLoading,
    isFetching,
    error,

    // Mutations
    createCollection: createCollectionMutation.mutate,
    isCreatingCollection: createCollectionMutation.isPending,

    addToCollection: addToCollectionMutation.mutate,
    isAddingToCollection: addToCollectionMutation.isPending,

    removeFromCollection: removeFromCollectionMutation.mutate,
    isRemovingFromCollection: removeFromCollectionMutation.isPending,

    // New function for favorite button
    removeFromAllCollections: removeFromAllCollectionsMutation.mutate,
    isRemovingFromAllCollections: removeFromAllCollectionsMutation.isPending,

    deleteCollection: deleteCollectionMutation.mutate,
    isDeletingCollection: deleteCollectionMutation.isPending,

    updateCollection: updateCollectionMutation.mutate,
    isUpdatingCollection: updateCollectionMutation.isPending,

    // Utilities - now implemented
    getCollectionItems,
    isPropertyInCollections,
    getCollectionsForProperty,
    useCollectionWithProperties,
    preloadCollectionImages,
    refetch,
  };
}

// New hook for getting a single collection by ID
export function useCollection(collectionId: string) {
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  return useQuery({
    queryKey: ['collection', collectionId],
    queryFn: async () => {
      const response = await collectionService.getCollectionById(collectionId);
      if (!response.status || !response.data) {
        throw new Error(response.message || 'Không thể tải bộ sưu tập');
      }
      return response.data;
    },
    enabled: !!collectionId && isAuthenticated,
    retry: (failureCount, error) => {
      // Don't retry if it's a not found error
      if (error?.message?.includes('Không tìm thấy')) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 5000),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: false,
  });
}
