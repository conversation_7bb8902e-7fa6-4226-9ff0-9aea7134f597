'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { APIProvider } from '@vis.gl/react-google-maps';
import { Button } from '@/components/ui/button';
import { ChevronDown, Calendar, DollarSign, Type, ArrowUpDown } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import PropertyListings from './PropertyListings';
import SearchFilter from './SearchFilter';
import PropertiesMap from './PropertiesMap';
import { useProperties } from '@/hooks/useProperty';
import {
  PropertySearchParams,
  PropertyType,
  TransactionType,
  PropertyDetailFilters,
} from '@/lib/api/services/fetchProperty';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

// Add SearchFilterSkeleton component
function SearchFilterSkeleton() {
  return (
    <section className="w-full max-w-screen mx-auto bg-background text-foreground  ">
      <div className="ml-4">
        <div className="w-full border-0 bg-card overflow-hidden">
          <div className="p-2 space-y-2">
            <div className="flex flex-row gap-2">
              {/* Search Input Skeleton */}
              <div className="max-xl:hidden relative w-full md:w-[300px] flex-shrink-0">
                <Skeleton className="h-10 w-full" />
              </div>

              {/* Filter Buttons Skeletons */}
              <div className="flex-1 flex flex-wrap gap-2 max-md:hidden">
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
              </div>

              {/* Search and More Filters Buttons Skeletons */}
              <div className="col-span-2 sm:col-span-3 md:col-span-4 flex flex-row gap-2">
                <Skeleton className="h-10 w-[100px]" />
                <Skeleton className="max-md:hidden h-10 w-[40px]" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Add MobileHeaderButtonSkeleton component
function MobileHeaderButtonSkeleton() {
  return (
    <div className="flex items-center gap-2">
      <Skeleton className="h-10 w-[200px] rounded-full" />
      <Skeleton className="h-6 w-6 rounded-full" />
    </div>
  );
}

// Add type definitions for sort options
type SortOption = {
  key: string;
  label: string;
  sortBy?: 'price' | 'createdAt' | 'name';
  sortOrder?: 'asc' | 'desc';
  icon: React.ComponentType<{ className?: string }>;
};

const sortOptions: SortOption[] = [
  {
    key: 'createdAt-desc',
    label: 'Mới nhất',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    icon: Calendar,
  },
  {
    key: 'price-asc',
    label: 'Giá thấp nhất',
    sortBy: 'price',
    sortOrder: 'asc',
    icon: DollarSign,
  },
  {
    key: 'price-desc',
    label: 'Giá cao nhất',
    sortBy: 'price',
    sortOrder: 'desc',
    icon: DollarSign,
  },
  {
    key: 'createdAt-asc',
    label: 'Cũ nhất',
    sortBy: 'createdAt',
    sortOrder: 'asc',
    icon: Calendar,
  },
  {
    key: 'name-asc',
    label: 'Tên A-Z',
    sortBy: 'name',
    sortOrder: 'asc',
    icon: Type,
  },
  {
    key: 'name-desc',
    label: 'Tên Z-A',
    sortBy: 'name',
    sortOrder: 'desc',
    icon: Type,
  },
  {
    key: 'default',
    label: 'Mặc định',
    icon: ArrowUpDown,
  },
];

export default function PropertiesClient() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [searchFilters, setSearchFilters] = useState<PropertySearchParams>({});
  const [hoveredPropertyId, setHoveredPropertyId] = useState<string | null>(null);
  const [panelWidth, setPanelWidth] = useState<number>(0);
  const panelRef = useRef<HTMLDivElement>(null);
  const itemsPerPage = 20;

  const page = Number(searchParams.get('page')) || 1;

  // Updated sort logic - default to newest first
  const getCurrentSortOption = (): SortOption => {
    const sortBy = searchParams.get('sortBy') as 'price' | 'createdAt' | 'name' | 'none' | null;
    const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' | null;

    if (sortBy === 'none') {
      // User explicitly selected "Mặc định"
      return sortOptions.find(opt => opt.key === 'default')!;
    } else if (sortBy && sortOrder) {
      // User selected a specific sort option
      const option = sortOptions.find(opt => opt.sortBy === sortBy && opt.sortOrder === sortOrder);
      return option || sortOptions[0]; // fallback to newest first
    } else {
      // No sort params - default to newest first
      return sortOptions[0]; // This is now "Mới nhất"
    }
  };

  const currentSortOption = getCurrentSortOption();

  const initialCenter =
    searchParams.has('lat') && searchParams.has('lng')
      ? {
          lat: Number(searchParams.get('lat')),
          lng: Number(searchParams.get('lng')),
        }
      : undefined;

  const initialZoom = searchParams.has('zoom') ? Number(searchParams.get('zoom')) : undefined;

  // Add effect to measure panel width
  useEffect(() => {
    const updatePanelWidth = () => {
      if (panelRef.current) {
        const width = panelRef.current.getBoundingClientRect().width;
        if (width > 0) {
          setPanelWidth(width);
        }
      }
    };

    // Initial measurement with a small delay
    const timer = setTimeout(updatePanelWidth, 100);

    // Set up resize observer
    const resizeObserver = new ResizeObserver(updatePanelWidth);
    if (panelRef.current) {
      resizeObserver.observe(panelRef.current);
    }

    return () => {
      clearTimeout(timer);
      resizeObserver.disconnect();
    };
  }, []);

  // Convert URL search params to server-side API filters
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    const filters: PropertySearchParams = {
      pageNumber: page,
      pageSize: itemsPerPage,
    };

    const sortBy = params.get('sortBy');
    const sortOrder = params.get('sortOrder');

    // Apply sorting logic
    if (sortBy === 'none') {
      // User explicitly selected "Mặc định" - no sorting
      // Don't set sortBy or isDescending
    } else if (sortBy && sortOrder) {
      // User selected a specific sort option
      filters.sortBy = sortBy as 'price' | 'createdAt' | 'name';
      filters.isDescending = sortOrder === 'desc';
    } else {
      // No sort params - default to newest first
      filters.sortBy = 'createdAt';
      filters.isDescending = true;
    }

    // Location filters from map bounds if available
    if (params.has('swLatitude')) {
      filters.swLatitude = Number(params.get('swLatitude'));
      filters.neLatitude = Number(params.get('neLatitude'));
      filters.swLongitude = Number(params.get('swLongitude'));
      filters.neLongitude = Number(params.get('neLongitude'));
    } else {
      // Fallback to text-based location if no map bounds
      if (params.has('city')) {
        if (!filters.searchTerm) {
          filters.searchTerm = params.get('city')!;
        }
      }
    }

    // Extract search term
    if (params.has('searchTerm')) {
      filters.searchTerm = params.get('searchTerm') || undefined;
    }

    // Map property type
    if (params.has('propertyType')) {
      const propertyType = params.get('propertyType');

      // Check if the propertyType is a valid enum value
      if (propertyType && Object.values(PropertyType).includes(propertyType as PropertyType)) {
        filters.type = propertyType as PropertyType;
      }
    }

    // Map transaction type
    if (params.has('transactionType')) {
      const transactionType = params.get('transactionType');
      if (
        transactionType === TransactionType.FOR_SALE ||
        transactionType === TransactionType.FOR_RENT
      ) {
        filters.transactionType = transactionType;
      }
    }

    // Location filters
    if (params.has('city')) {
      // Note: City filtering will be handled differently in future
      // For now, we'll pass it as searchTerm if no other search term exists
      if (!filters.searchTerm && params.get('city')) {
        filters.searchTerm = params.get('city')!;
      }
    }

    // Price range filters
    if (params.has('minPrice')) {
      filters.minPrice = Number(params.get('minPrice')) || undefined;
    }
    if (params.has('maxPrice')) {
      filters.maxPrice = Number(params.get('maxPrice')) || undefined;
    }

    // Area filters
    if (params.has('minArea')) {
      filters.minLandArea = Number(params.get('minArea')) || undefined;
    }
    if (params.has('maxArea')) {
      filters.maxLandArea = Number(params.get('maxArea')) || undefined;
    }

    // Bedroom filters
    if (params.has('bedCount')) {
      const bedCount = params.get('bedCount');
      const isExactMatch = params.get('exactBedMatch') === 'true';

      if (bedCount && bedCount !== 'any') {
        if (bedCount === 'studio') {
          filters.bedrooms = 0;
        } else {
          const beds = parseInt(bedCount);
          if (isExactMatch) {
            filters.bedrooms = beds;
          } else {
            filters.minBedrooms = beds;
          }
        }
      }
    }

    // Bathroom filters
    if (params.has('bathCount')) {
      const bathCount = params.get('bathCount');
      if (bathCount && bathCount !== 'any') {
        const baths = parseInt(bathCount.replace('+', ''));
        filters.minBathrooms = baths;
      }
    }

    // Living Room and Kitchen filters
    if (params.has('livingRoomCount')) {
      const livingRoomCount = params.get('livingRoomCount');
      if (livingRoomCount && livingRoomCount !== 'any') {
        const livingRooms = parseInt(livingRoomCount.replace('+', ''));
        filters.minLivingRooms = livingRooms;
      }
    }
    if (params.has('kitchenCount')) {
      const kitchenCount = params.get('kitchenCount');
      if (kitchenCount && kitchenCount !== 'any') {
        const kitchens = parseInt(kitchenCount.replace('+', ''));
        filters.minKitchens = kitchens;
      }
    }

    // Amenity and Detail filters
    if (params.has('amenityFilters')) {
      filters.amenityFilters = params.get('amenityFilters')?.split(',') || undefined;
    }
    if (params.has('propertyDetailFilters')) {
      const details = params.get('propertyDetailFilters')?.split(',');
      if (details) {
        filters.propertyDetailFilters = details.map(
          d => d as PropertyDetailFilters
        ) as PropertyDetailFilters[];
      }
    }

    setSearchFilters(filters);
  }, [searchParams, page, currentSortOption]);

  // Fetch properties with server-side filtering
  const { properties, isLoading, isError, error, isFetching, count, totalPages } =
    useProperties(searchFilters);

  const handleMapIdle = useCallback(
    (mapState: {
      bounds: google.maps.LatLngBounds;
      center: google.maps.LatLngLiteral;
      zoom: number;
    }) => {
      const newParams = new URLSearchParams(searchParams.toString());
      const { bounds, center, zoom } = mapState;
      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();

      newParams.set('swLatitude', sw.lat().toString());
      newParams.set('neLatitude', ne.lat().toString());
      newParams.set('swLongitude', sw.lng().toString());
      newParams.set('neLongitude', ne.lng().toString());

      newParams.set('lat', center.lat.toString());
      newParams.set('lng', center.lng.toString());
      newParams.set('zoom', zoom.toString());

      // Remove city/district/ward to prioritize map search
      newParams.delete('city');
      newParams.delete('district');
      newParams.delete('ward');

      router.replace(`/properties?${newParams.toString()}`);
    },
    [router, searchParams]
  );

  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set('page', newPage.toString());
    router.push(`/properties?${newParams.toString()}`);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getTitleText = () => {
    const city = searchParams.get('city') || 'Hồ Chí Minh';
    const transactionType = searchParams.get('transactionType')?.toLowerCase();

    if (
      transactionType === TransactionType.FOR_SALE ||
      transactionType === 'forsale' ||
      transactionType === 'for_sale'
    ) {
      return `Nhà bán ở ${city}`;
    } else if (
      transactionType === TransactionType.FOR_RENT ||
      transactionType === 'forrent' ||
      transactionType === 'for_rent'
    ) {
      return `Nhà cho thuê tại ${city}`;
    }
    return `Bất động sản ở ${city}`;
  };

  const handleSortChange = (option: SortOption) => {
    const newParams = new URLSearchParams(searchParams.toString());

    if (option.key === 'default') {
      // Set special value for no sorting
      newParams.set('sortBy', 'none');
      newParams.delete('sortOrder');
    } else {
      // Set the specific sort option
      newParams.set('sortBy', option.sortBy!);
      newParams.set('sortOrder', option.sortOrder!);
    }

    // Reset to first page when changing sort
    newParams.set('page', '1');
    router.push(`/properties?${newParams.toString()}`);
  };

  // Calculate pagination - Server already handles this
  const paginatedProperties = properties;

  return (
    <div className=" h-[calc(100vh-10rem)] md:h-[calc(100vh-4rem)] flex flex-col">
      <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
        {/* Mobile Header */}
        <div className="bg-background border-b flex-shrink-0 md:hidden">
          <div className="flex items-center px-2">
            {isLoading ? (
              <MobileHeaderButtonSkeleton />
            ) : (
              <Drawer>
                <DrawerTrigger asChild>
                  <Button
                    variant="outline"
                    className="rounded-full border-primary border-dashed shadow-none"
                  >
                    {getTitleText()}
                    <Badge variant="destructive" className="rounded-full px-1 py-px">
                      {count}+
                    </Badge>
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="h-[90vh]  ">
                  <DrawerHeader>
                    <DrawerTitle>{getTitleText()}</DrawerTitle>
                    <DrawerDescription>{count} bất động sản</DrawerDescription>
                  </DrawerHeader>

                  <div className="flex-1 overflow-y-auto p-4">
                    <PropertyListings
                      properties={paginatedProperties}
                      isLoading={isLoading}
                      isError={isError}
                      error={error}
                      isFetching={isFetching}
                      count={count}
                      totalPages={totalPages}
                      page={page}
                      onPageChange={handlePageChange}
                      onPropertyHover={setHoveredPropertyId}
                      containerWidth={panelWidth}
                    />
                  </div>
                </DrawerContent>
              </Drawer>
            )}

            <div className="flex-1">{isLoading ? <SearchFilterSkeleton /> : <SearchFilter />}</div>
          </div>
        </div>

        {/* Desktop Header */}
        <div className="bg-background border-b flex-shrink-0 hidden md:block">
          {isLoading ? <SearchFilterSkeleton /> : <SearchFilter />}
        </div>

        {/* Mobile Layout */}
        <div className="flex-1 min-h-0 md:hidden flex flex-col">
          <div className="h-full flex-shrink-0 relative touch-none">
            <div className="absolute inset-0">
              <PropertiesMap
                properties={properties}
                onMapIdle={handleMapIdle}
                hoveredPropertyId={hoveredPropertyId}
                initialCenter={initialCenter}
                initialZoom={initialZoom}
              />
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="flex-1 min-h-0 hidden md:flex">
          <ResizablePanelGroup direction="horizontal" className="h-full">
            <ResizablePanel defaultSize={50} minSize={30}>
              <div className="h-full relative">
                <PropertiesMap
                  properties={properties}
                  onMapIdle={handleMapIdle}
                  hoveredPropertyId={hoveredPropertyId}
                  initialCenter={initialCenter}
                  initialZoom={initialZoom}
                />
              </div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={50} minSize={30}>
              <div className="h-full flex flex-col" ref={panelRef}>
                <div className="flex-shrink-0 p-4 border-b">
                  <div className="flex justify-between items-center">
                    <h1 className="text-lg font-semibold">
                      {getTitleText()}{' '}
                      <span className="text-sm text-muted-foreground">- {count} bất động sản</span>
                    </h1>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="flex items-center gap-2">
                          <ArrowUpDown className="h-4 w-4" />
                          {currentSortOption.label}
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48 shadow-none">
                        <DropdownMenuGroup>
                          {sortOptions.map(option => (
                            <DropdownMenuItem
                              key={option.key}
                              onClick={() => handleSortChange(option)}
                              className={option.key === currentSortOption.key ? 'bg-accent' : ''}
                            >
                              <option.icon className="mr-2 h-4 w-4" />
                              {option.label}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuGroup>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                <div className="flex-1 overflow-y-auto p-4">
                  <PropertyListings
                    properties={paginatedProperties}
                    isLoading={isLoading}
                    isError={isError}
                    error={error}
                    isFetching={isFetching}
                    count={count}
                    totalPages={totalPages}
                    page={page}
                    onPageChange={handlePageChange}
                    onPropertyHover={setHoveredPropertyId}
                    containerWidth={panelWidth}
                  />
                </div>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </APIProvider>
    </div>
  );
}
