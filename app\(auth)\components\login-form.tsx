'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { useState } from 'react';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';
import { GoogleAuthButton } from '@/components/auth/googleAuthButton';
import { Separator } from '@/components/ui/separator';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { LoginOTPDialog } from '@/components/auth/LoginOTPDialog';
import { FacebookAuthButton } from '@/components/auth/facebookAuthButton';
import { ZaloAuthButton } from '@/components/auth/zaloAuthButton';

const loginSchema = z.object({
  keyLogin: z
    .string()
    .toLowerCase()
    .min(1, 'Email hoặc số điện thoại là bắt buộc')
    .refine(
      value => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const phoneRegex = /^(\+84|84|0)[35789][0-9]{8}$/;
        return emailRegex.test(value) || phoneRegex.test(value);
      },
      {
        message: 'Email hoặc số điện thoại không hợp lệ',
      }
    ),
  password: z.string().min(1, 'Mật khẩu là bắt buộc'),
});

export function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const { login, isLoading, needsOtpVerification, loginVerifyKey, clearLoginOtpState } = useAuth();
  const [googleError, setGoogleError] = useState<string | null>(null);
  const [facebookError, setFacebookError] = useState<string | null>(null);
  const [zaloError, setZaloError] = useState<string | null>(null);

  const handleGoogleError = (error: string | null) => {
    setFacebookError(null);
    setZaloError(null);
    setGoogleError(error);
  };

  const handleFacebookError = (error: string | null) => {
    setGoogleError(null);
    setZaloError(null);
    setFacebookError(error);
  };

  const handleZaloError = (error: string | null) => {
    setGoogleError(null);
    setFacebookError(null);
    setZaloError(error);
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
  });

  const onSubmit = async (data: z.infer<typeof loginSchema>) => {
    try {
      // Add rememberMe to the data before sending to login
      const loginData = { ...data, rememberMe };
      login(loginData);
    } catch (error) {
      toast.error('Đã có lỗi xảy ra khi đăng nhập. Vui lòng thử lại sau.');
    }
  };

  const handleCloseOtpDialog = () => {
    clearLoginOtpState();
  };

  return (
    <>
      <div className="flex flex-col items-center justify-center  ">
        <div className="w-full space-y-6 rounded-xl">
          <div className="flex flex-col items-center gap-2 text-center">
            <h1 className="text-xl md:text-2xl font-bold tracking-tight text-red-500">
              Đăng nhập tài khoản của bạn
            </h1>
            <p className="text-balance text-xs md:text-sm text-muted-foreground">
              Nhập thông tin đăng nhập của bạn
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="keyLogin" className="text-xs md:text-sm">
                  Email hoặc số điện thoại
                  <span className="text-destructive"> *</span>
                </Label>
                <Input
                  id="keyLogin"
                  {...register('keyLogin')}
                  type="text"
                  placeholder="Nhập email hoặc số điện thoại"
                  className={cn(errors.keyLogin && 'border-destructive text-xs md:text-sm')}
                  aria-describedby="keyLogin-error"
                />
                {errors.keyLogin && (
                  <p className="text-xs text-destructive" id="keyLogin-error">
                    {errors.keyLogin.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className="text-xs md:text-sm">
                    Mật khẩu
                    <span className="text-destructive"> *</span>
                  </Label>
                  <Link
                    href="/forgot-password"
                    className="text-xs md:text-sm text-primary hover:text-primary/90 hover:underline"
                  >
                    Quên mật khẩu?
                  </Link>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    {...register('password')}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Nhập mật khẩu"
                    className={cn(errors.password && 'border-destructive pr-10 text-xs md:text-sm')}
                    aria-describedby="password-error"
                  />

                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1 h-8 w-8 px-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                    <span className="sr-only">
                      {showPassword ? 'Ẩn mật khẩu' : 'Hiển thị mật khẩu'}
                    </span>
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-xs text-destructive" id="password-error">
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="rememberMe"
                  checked={rememberMe}
                  onCheckedChange={checked => setRememberMe(!!checked)}
                  className="data-[state=checked]:bg-red-500 data-[state=checked]:border-red-500"
                />
                <Label
                  htmlFor="rememberMe"
                  className="text-xs md:text-sm font-normal cursor-pointer"
                >
                  Ghi nhớ đăng nhập
                </Label>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-red-500 hover:bg-red-600"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}
                </>
              ) : (
                'Đăng nhập'
              )}
            </Button>
          </form>

          <div className="relative flex items-center justify-center">
            <Separator className="flex-1 border-t border-dashed bg-transparent" />
            <span className="px-2 text-xs md:text-sm text-muted-foreground">
              Hoặc đăng nhập với
            </span>
            <Separator className="flex-1 border-t border-dashed bg-transparent" />
          </div>
          <div className="flex justify-center w-full">
            <div className="space-y-4 w-full max-w-[400px]">
              <FacebookAuthButton mode="login" onError={handleFacebookError} />
              <GoogleAuthButton mode="login" onError={handleGoogleError} />
              <ZaloAuthButton mode="login" onError={handleZaloError} />

              {(googleError || facebookError || zaloError) && (
                <div className="rounded-md bg-destructive/10 p-2 text-center text-xs md:text-sm text-destructive">
                  {googleError || facebookError || zaloError}
                </div>
              )}
            </div>
          </div>
          <div className="text-center">
            <p className="text-xs md:text-sm text-muted-foreground">
              Không có tài khoản?{' '}
              <Link href="/register" className="text-primary hover:underline">
                Đăng ký
              </Link>
            </p>
          </div>

          <div className="text-balance text-center text-xs md:text-sm text-muted-foreground">
            Bằng cách tiếp tục, bạn đồng ý với{' '}
            <Link href="/legal/terms-of-agreement" className="hover:text-primary hover:underline">
              Điều khoản, điều kiện
            </Link>{' '}
            và{' '}
            <Link href="/legal/privacy-policy" className="hover:text-primary hover:underline">
              Chính sách bảo mật
            </Link>
            .
          </div>
        </div>
      </div>

      {needsOtpVerification && loginVerifyKey && (
        <LoginOTPDialog
          isOpen={needsOtpVerification}
          onClose={handleCloseOtpDialog}
          verifyKey={loginVerifyKey}
        />
      )}
    </>
  );
}
