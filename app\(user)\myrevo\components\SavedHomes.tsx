import { Skeleton } from '@/components/ui/skeleton';
import { Property } from '@/lib/api/services/fetchProperty';
import { useFavoriteProperty } from '@/hooks/useFavoriteProperty';
import { useAuth } from '@/lib/providers/authProvider';
import { useState } from 'react';
import { PropertyCard } from '@/components/PropertyCard';
import { useIsMobile } from '@/hooks/useMobile';
import BottomComparison from '@/components/BottomComparison';
import { TooltipProvider } from '@/components/ui/tooltip';

export default function SavedHomes() {
  const isMobile = useIsMobile();
  const { favoriteProperties, isLoading, isError } = useFavoriteProperty();
  const { isAuthenticated } = useAuth();
  const [isComparisonVisible, setIsComparisonVisible] = useState(false);
  const [selectedProperties, setSelectedProperties] = useState<Property[]>([]);
  console.log('Favorite Properties', favoriteProperties);
  // Don't show anything if not authenticated (auth dialog will handle this)
  if (!isAuthenticated) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Vui lòng đăng nhập để xem nhà đã lưu</p>
      </div>
    );
  }

  const handleCompareClick = (property: Property) => {
    setIsComparisonVisible(true);
    setSelectedProperties(prev => {
      const alreadyExists = prev.find(p => p.id === property.id);
      if (alreadyExists || prev.length >= 5) return prev;
      return [...prev, property];
    });
  };

  const handleRemoveFromComparison = (id: string) => {
    setSelectedProperties(prev => prev.filter(p => p.id !== id));
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="space-y-3 md:space-y-4">
            <Skeleton className="aspect-square w-full rounded-xl" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Lỗi khi tải dữ liệu</p>
      </div>
    );
  }

  if (!favoriteProperties?.data || favoriteProperties.data.properties.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">Bạn chưa lưu bất động sản nào</div>
    );
  }

  return (
    <TooltipProvider>
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 gap-4 2xl:grid-cols-4 md:gap-6">
        {favoriteProperties.data.properties.map((property: Property) => (
          <PropertyCard
            key={property.id}
            size={isMobile ? 'sm' : 'md'}
            property={property}
            onCompareClick={handleCompareClick}
            selectedComparisonIds={selectedProperties.map(p => p.id)}
          />
        ))}
      </div>

      {isComparisonVisible && (
        <BottomComparison
          selectedProperties={selectedProperties}
          onRemove={handleRemoveFromComparison}
        />
      )}
    </TooltipProvider>
  );
}
