'use client';

import * as React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile } from '@/hooks/useUsers';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  UserCircleIcon,
  LogOutIcon,
  Menu,
  LayoutDashboardIcon,
  UsersIcon,
  TrendingUpIcon,
  HeartIcon,
  CalendarIcon,
  PlusIcon,
  BuildingIcon,
  ChevronRightIcon,
  ClockIcon,
} from 'lucide-react';
import { NavigationBar, navigationData, DropdownContent } from './NavigationBar';
import { motion } from 'framer-motion';
import { Sheet, SheetContent, She<PERSON><PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from '@/components/ui/sheet';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from './ui/scroll-area';

export default function Header() {
  const { isAuthenticated, logout } = useAuth();
  const { data: profileData, error: profileError } = useUserProfile();

  // Dropdown state management
  const [activeDropdown, setActiveDropdown] = React.useState<string | null>(null);

  const handleMouseEnter = (key: string) => {
    setActiveDropdown(key);
  };

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  // No body scroll lock - let the page remain scrollable

  console.log(profileError);
  // Get user data from profile response, with fallback for unauthenticated users
  const user =
    profileData?.profile && isAuthenticated
      ? {
          name: profileData.profile.fullName,
          email: profileData.profile.email,
          avatar: profileData.profile.avatar || '',
        }
      : {
          name: '',
          email: '',
          avatar: '',
        };

  // Create initials from name for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const initials = getInitials(user.name);

  return (
    <>
      <header
        className={`w-full bg-background text-foreground sticky top-0 z-50 h-14 flex-shrink-0 shadow-sm`}
      >
        <div className="container mx-auto px-4 h-full">
          <div className="flex h-full items-center justify-between">
            {/* Logo and Mobile Menu */}
            <div className="flex items-center gap-4">
              <Sheet>
                <SheetTrigger asChild className="md:hidden">
                  <Button variant="ghost" size="icon" className="h-9 w-9">
                    <Menu className="h-5 w-5" />
                    <span className="sr-only">Toggle menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-[300px] sm:w-[400px]">
                  <SheetHeader>
                    <SheetTitle>Menu</SheetTitle>
                  </SheetHeader>
                  <ScrollArea className="h-full">
                    <div className="flex flex-col gap-2 p-2 pt-0 pb-10">
                      {/* Mobile Navigation Menu */}
                      {Object.entries(navigationData).map(([key, data], index) => (
                        <motion.div
                          key={key}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.1 }}
                        >
                          <Collapsible defaultOpen={false} className="space-y-2">
                            <CollapsibleTrigger className="flex items-center justify-between w-full py-2 px-4 text-left rounded-md hover:bg-accent transition-colors">
                              <span className="font-normal text-base capitalize">{data.title}</span>
                              <ChevronRightIcon className='size-4 shrink-0 transition-transform [[data-state="open"]>&]:rotate-90' />
                            </CollapsibleTrigger>
                            <CollapsibleContent className="data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down overflow-hidden transition-all duration-300">
                              <div className="p-2 space-y-4">
                                {/* Column 1 */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.1 }}
                                >
                                  <h4 className="font-normal text-sm text-muted-foreground mb-2">
                                    {data.column1.title}
                                  </h4>
                                  <div className="space-y-1">
                                    {data.column1.items.map((item, itemIndex) => (
                                      <motion.div
                                        key={item.title}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{
                                          duration: 0.3,
                                          delay: 0.2 + itemIndex * 0.05,
                                        }}
                                      >
                                        <Link
                                          href={item.href}
                                          className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 transition-colors group"
                                        >
                                          <span className="font-medium text-sm text-foreground group-hover:text-red-600">
                                            {item.title}
                                          </span>
                                        </Link>
                                      </motion.div>
                                    ))}
                                  </div>
                                </motion.div>

                                {/* Column 2 */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.3 }}
                                >
                                  <h4 className="font-normal text-sm text-muted-foreground mb-2">
                                    {data.column2.title}
                                  </h4>
                                  <div className="space-y-1">
                                    {data.column2.items.map((item, itemIndex) => (
                                      <motion.div
                                        key={item.title}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{
                                          duration: 0.3,
                                          delay: 0.4 + itemIndex * 0.04,
                                        }}
                                      >
                                        <Link
                                          href={item.href}
                                          className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 transition-colors group"
                                        >
                                          <span className="font-medium text-sm text-foreground group-hover:text-red-600">
                                            {item.title}
                                          </span>
                                        </Link>
                                      </motion.div>
                                    ))}
                                  </div>
                                </motion.div>

                                {/* Spotlight Section */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.5 }}
                                  className="border-t border-gray-200 pt-4"
                                >
                                  <h4 className="font-normal text-sm text-muted-foreground mb-3">
                                    Spotlight
                                  </h4>
                                  <Link href={data.spotlight.href} className="block group">
                                    <div className="space-y-3">
                                      <h5 className="font-medium text-sm text-gray-900 group-hover:text-red-600 transition-colors leading-tight">
                                        {data.spotlight.title}
                                      </h5>
                                      <p className="text-xs text-gray-600 leading-relaxed line-clamp-2">
                                        {data.spotlight.description}
                                      </p>
                                      <div className="relative aspect-video rounded-lg overflow-hidden">
                                        <Image
                                          src={data.spotlight.image}
                                          alt={data.spotlight.title}
                                          fill
                                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                                        />
                                      </div>
                                      <div className="flex items-center gap-2 text-xs text-gray-500">
                                        <ClockIcon className="h-3 w-3" />
                                        <span>{data.spotlight.readTime}</span>
                                      </div>
                                    </div>
                                  </Link>
                                </motion.div>

                                {/* Description Section */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.6 }}
                                  className="border-t border-gray-200 pt-4"
                                >
                                  <h4 className="font-normal text-sm text-muted-foreground mb-2">
                                    Về {data.title}
                                  </h4>
                                  <p className="text-xs text-gray-600 leading-relaxed mb-3">
                                    {data.description}
                                  </p>
                                  <Link
                                    href="/properties"
                                    className="inline-flex items-center text-xs font-medium text-red-600 hover:text-red-700 transition-colors"
                                  >
                                    Xem tất cả →
                                  </Link>
                                </motion.div>
                              </div>
                            </CollapsibleContent>
                          </Collapsible>
                        </motion.div>
                      ))}

                      {/* Additional Menu Items */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.4 }}
                        className="space-y-2"
                      >
                        <Link
                          href="/myrevo"
                          className="flex items-center gap-2 py-2 px-4 rounded-md hover:bg-accent transition-colors"
                        >
                          <span className="font-normal text-base">Tài khoản</span>
                        </Link>
                        <Link
                          href="/comparison"
                          className="flex items-center gap-2 py-2 px-4 rounded-md hover:bg-accent transition-colors"
                        >
                          <span className="font-normal text-base">So sánh</span>
                        </Link>
                      </motion.div>
                    </div>
                  </ScrollArea>
                </SheetContent>
              </Sheet>

              <Link href="/" className="flex items-center">
                <Image
                  src="/logo_revoland_red.png"
                  alt="Revoland icon"
                  width={40}
                  height={40}
                  priority
                  className="rounded-md"
                />
                <span className="lg:block hidden text-2xl font-medium text-red-500">Revoland</span>
                {/* <div className="lg:block hidden">
                  <Image
                    src="/revoland_text.png"
                    alt="Revoland text"
                    width={120}
                    height={120}
                    priority
                    className="rounded-md"
                  />
                </div> */}
              </Link>
            </div>

            {/* Desktop Navigation - Centered absolutely */}
            <div className="hidden md:block absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <NavigationBar
                activeDropdown={activeDropdown}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              />
            </div>

            {/* Auth Buttons */}
            <div className="flex items-center gap-2">
              {isAuthenticated ? (
                <>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="relative size-10 rounded-full">
                        <Avatar className="size-10 ring-zinc-300 ring-2">
                          <AvatarImage src={user.avatar} alt={user.name} className="object-cover" />
                          <AvatarFallback className="bg-red-500/10 text-red-500">
                            {initials}
                          </AvatarFallback>
                        </Avatar>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-64" align="end" forceMount>
                      <DropdownMenuLabel className="font-normal">
                        <div className="flex flex-col space-y-1">
                          <p className="text-sm font-medium leading-none">{user.name}</p>
                          <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {/* Quick Actions Section */}
                      <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                        Tin đăng
                      </DropdownMenuLabel>
                      <DropdownMenuGroup>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/property/action" className="cursor-pointer">
                            <PlusIcon className="mr-2 h-4 w-4" />
                            Đăng tin bất động sản
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/property" className="cursor-pointer">
                            <BuildingIcon className="mr-2 h-4 w-4" />
                            Quản lý bất động sản
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>

                      <DropdownMenuSeparator />

                      {/* Dashboard Section */}
                      <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                        Quản lý
                      </DropdownMenuLabel>
                      <DropdownMenuGroup>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/dashboard" className="cursor-pointer">
                            <LayoutDashboardIcon className="mr-2 h-4 w-4" />
                            Dashboard
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/lead" className="cursor-pointer">
                            <UsersIcon className="mr-2 h-4 w-4" />
                            Quản lý khách hàng
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/sales" className="cursor-pointer">
                            <TrendingUpIcon className="mr-2 h-4 w-4" />
                            Quản lý giao dịch
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>

                      <DropdownMenuSeparator />

                      {/* User Account Section */}
                      <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                        Tài khoản
                      </DropdownMenuLabel>
                      <DropdownMenuGroup>
                        <DropdownMenuItem asChild>
                          <Link href="/myrevo" className="cursor-pointer">
                            <UserCircleIcon className="mr-2 h-4 w-4" />
                            Thông tin cá nhân
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/myrevo?tab=saved" className="cursor-pointer">
                            <HeartIcon className="mr-2 h-4 w-4" />
                            Bất động sản đã lưu
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/myrevo?tab=appointments" className="cursor-pointer">
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            Lịch hẹn xem nhà
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/myrevo?tab=recent" className="cursor-pointer">
                            <ClockIcon className="mr-2 h-4 w-4" />
                            Đã xem gần đây
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>

                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => logout()} className="cursor-pointer">
                        <LogOutIcon className="mr-2 h-4 w-4" />
                        Đăng xuất
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              ) : (
                <div className="flex items-center gap-2">
                  {/* <Button asChild variant="ghost" size="sm" className="hidden sm:inline-flex">
                    <Link href="/register">Đăng ký</Link>
                  </Button> */}
                  <Button asChild variant="ghost" size="sm" className="max-md:hidden">
                    <Link href="/login?redirect=/">Đăng nhập</Link>
                  </Button>
                  <Button
                    asChild
                    variant="default"
                    size="sm"
                    className="bg-red-500 hover:bg-red-600"
                  >
                    <Link href="/login?redirect=/saler/property">Đăng tin</Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Dropdown - Auto height content */}
      {activeDropdown && (
        <div
          className="fixed left-0 right-0 z-50 bg-white/85 backdrop-blur-lg pointer-events-none"
          style={{
            top: '56px', // Height of header (h-14 = 56px)
            width: '100vw',
            margin: '0',
            padding: '0',
          }}
        >
          {/* Content area that detects mouse leave */}
          <div className="w-full pointer-events-auto" onMouseLeave={handleMouseLeave}>
            <DropdownContent data={navigationData[activeDropdown as keyof typeof navigationData]} />
          </div>
        </div>
      )}
    </>
  );
}
