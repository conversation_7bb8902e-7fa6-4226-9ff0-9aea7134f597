'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Phone, Mail, MessageCircle, MapPin } from 'lucide-react';

// Mock data for static UI
const mockSellerData = {
  id: '1',
  fullName: '<PERSON><PERSON><PERSON><PERSON> Sơn',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1740&auto=format&fit=crop',
  title: '<PERSON><PERSON><PERSON>n viên tư vấn bất động sản',
  company: 'CÔNG TY TNHH CÔNG NGHỆ BẤT ĐỘNG SẢN REVOLAND',
  phoneNumber: '(*************',
  email: '<EMAIL>',
  socialMedia: {
    facebook: 'https://facebook.com/nguyenvanan',
    linkedin: 'https://linkedin.com/in/nguyenvanan',
  },
};

function SellerProfileContact() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    contactMethod: 'email',
    message: '',
  });


  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission - integrate with CRM later
    console.log('Contact form submitted:', formData);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
      {/* Agent Info Card - Left Side */}
      <div className="space-y-6">
        <div className="relative">
          <Image
            src="https://images.unsplash.com/photo-1714974528683-8940bd8311aa?q=80&w=2064&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            alt="Agent"
            width={400}
            height={256}
            className="w-full h-64 object-cover rounded-2xl shadow-lg"
          />
        </div>

        <div className="space-y-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Liên hệ</h2>
            <p className="text-gray-600">Có câu hỏi gì? Chúng tôi sẵn sàng hỗ trợ bạn!</p>
          </div>

          <div className="space-y-3">
            <div className="flex items-center border-2 rounded-full space-x-3 p-3 hover:bg-gray-50 transition-colors">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                <Phone className="w-6 h-6 text-gray-700" />
              </div>
              <span className="font-medium text-gray-900">{mockSellerData.phoneNumber}</span>
            </div>

            {mockSellerData.email !== 'Chưa cập nhật' && (
              <div className="flex items-center space-x-3 p-3 bg-red-500 rounded-full border-2 border-gray-300">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                  <Mail className="w-6 h-6 text-gray-900" />
                </div>
                <span className="font-medium text-gray-200">{mockSellerData.email}</span>
              </div>
            )}

            {mockSellerData.company !== 'Chưa cập nhật' && (
              <div className="flex items-center border-2 rounded-full space-x-3 p-3 hover:bg-gray-50 transition-colors">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                  <MapPin className="w-6 h-6 text-gray-700" />
                </div>
                <span className="font-medium text-gray-900">{mockSellerData.company}</span>
              </div>
            )}

            <div className="flex items-center border-2 rounded-full space-x-3 p-3 hover:bg-gray-50 transition-colors">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                <MessageCircle className="w-6 h-6 text-gray-700" />
              </div>
              <span className="font-medium text-gray-900">Thêm từ {mockSellerData.fullName}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Form - Right Side */}
      <div className="bg-white rounded-2xl border border-gray-200 p-8 shadow-sm">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name Fields */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">
                Tên <span className="text-red-500">(bắt buộc)</span>
              </Label>
              <Input
                id="firstName"
                placeholder="Nhập tên của bạn"
                value={formData.firstName}
                onChange={e => handleInputChange('firstName', e.target.value)}
                className="rounded-lg border-gray-300 focus:border-red-400 focus:ring-lime-400"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">
                Họ <span className="text-red-500">(bắt buộc)</span>
              </Label>
              <Input
                id="lastName"
                placeholder="Nhập họ của bạn"
                value={formData.lastName}
                onChange={e => handleInputChange('lastName', e.target.value)}
                className="rounded-lg border-gray-300 focus:border-red-400 focus:ring-lime-400"
                required
              />
            </div>
          </div>

          {/* Contact Method */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">
              Liên hệ qua <span className="text-red-500">(bắt buộc)</span>
            </Label>
            <div className="bg-white border-2 rounded-lg p-1 flex">
              <button
                type="button"
                onClick={() => handleInputChange('contactMethod', 'email')}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all ${
                  formData.contactMethod === 'email'
                    ? 'bg-gray-200 text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Email
              </button>
              <button
                type="button"
                onClick={() => handleInputChange('contactMethod', 'phone')}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all ${
                  formData.contactMethod === 'phone'
                    ? 'bg-gray-200 text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Điện thoại
              </button>
              <button
                type="button"
                onClick={() => handleInputChange('contactMethod', 'both')}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all ${
                  formData.contactMethod === 'both'
                    ? 'bg-gray-200 text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Cả hai
              </button>
            </div>
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email-input" className="text-sm font-medium text-gray-700">
              Email <span className="text-red-500">(bắt buộc)</span>
            </Label>
            <Input
              id="email-input"
              type="email"
              placeholder="Nhập email của bạn"
              value={formData.email}
              onChange={e => handleInputChange('email', e.target.value)}
              className="rounded-lg border-gray-300 focus:border-red-400 focus:ring-lime-400"
              required
            />
          </div>

          {/* Phone */}
          <div className="space-y-2">
            <Label htmlFor="phone-input" className="text-sm font-medium text-gray-700">
              Điện thoại
            </Label>
            <Input
              id="phone-input"
              type="tel"
              placeholder="Nhập số điện thoại"
              value={formData.phone}
              onChange={e => handleInputChange('phone', e.target.value)}
              className="rounded-lg border-gray-300 focus:border-red-400 focus:ring-lime-400"
            />
          </div>

          {/* Message */}
          <div className="space-y-2">
            <Label htmlFor="message" className="text-sm font-medium text-gray-700">
              Tin nhắn
            </Label>
            <Textarea
              id="message"
              placeholder="Nhập tin nhắn của bạn"
              rows={6}
              value={formData.message}
              onChange={e => handleInputChange('message', e.target.value)}
              className="rounded-lg border-gray-300 focus:border-red-400 focus:ring-lime-400 resize-none"
            />
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full bg-red-500 hover:bg-red-400 text-gray-200 font-semibold py-3 rounded-full transition-colors"
          >
            Gửi tin nhắn
          </Button>
        </form>
      </div>
    </div>
  );
}

export default SellerProfileContact;
