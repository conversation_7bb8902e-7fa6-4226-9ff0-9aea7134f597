'use client';

import SellerProfileHeader from './SellerProfileHeader';
import SellerProfileStats from './SellerProfileStats';
import SellerProfileAbout from './SellerProfileAbout';
import SellerProfileReviewsWrapper from './SellerProfileReviewsWrapper';
import SellerProfileContact from './SellerProfileContact';
import SellerProfileListings from './SellerProfileListings';
import Footer from '@/components/Footer';
import { useSellerProfile } from '@/hooks/useUsers';
import { Loader2 } from 'lucide-react';
import { notFound } from 'next/navigation';

interface SellerProfileClientProps {
  sellerId: string;
}

// Mock data removed - now using real API data

export default function SellerProfileClient({ sellerId }: SellerProfileClientProps) {
  const { data: sellerData, isLoading, error } = useSellerProfile(sellerId);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Đang tải thông tin môi giới...</span>
        </div>
      </div>
    );
  }

  if (error || !sellerData?.profile) {
    notFound();
  }

  const profile = sellerData.profile;

  // Transform API data to match component props
  const transformedSellerData = {
    id: profile.sellerId,
    fullName: profile.name || 'Chưa cập nhật',
    avatar: profile.avatar || '/images/default-avatar.jpg',
    coverImage: profile.coverPhoto || '/images/default-cover.jpg',
    title: 'Chuyên viên bất động sản',
    company: profile.companyName || 'Chưa cập nhật',
    rating: 4.5, // Default rating since not provided by API
    reviewCount: profile.agentFeedbacks?.length || 0,
    phoneNumber: profile.phone || 'Chưa cập nhật',
    email: 'Chưa cập nhật', // Not provided by API
    experience: profile.yearsOfExperience ? `${profile.yearsOfExperience} năm` : 'Chưa cập nhật',
    specialties: profile.fieldOfWorks ? [profile.fieldOfWorks] : ['Bất động sản'],
    about: profile.introduction || 'Chưa có thông tin giới thiệu.',
    joinedAt: profile.joinedAt,
    serviceLocations: profile.serviceLocations,
    stats: {
      totalSales: 0, // Not provided by API
      totalListings: profile.properties?.length || 0,
      averageDaysOnMarket: 0, // Not provided by API
      clientSatisfaction: 0, // Not provided by API
      joinedAt: profile.joinedAt,
      yearsOfExperience: profile.yearsOfExperience,
    },
    socialMedia: {
      facebook: '',
      linkedin: '',
      twitter: '',
    },
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header with cover image and basic info */}
      <SellerProfileHeader seller={transformedSellerData} />

      {/* Stats section */}
      <SellerProfileStats stats={transformedSellerData.stats} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* About section */}
          <SellerProfileAbout
            about={transformedSellerData.about}
            specialties={transformedSellerData.specialties}
            experience={transformedSellerData.experience}
            joinedAt={transformedSellerData.joinedAt}
            serviceLocations={transformedSellerData.serviceLocations}
          />

          {/* Contact section - moved to main content */}
          <SellerProfileContact />

          {/* Reviews section */}
          <SellerProfileReviewsWrapper sellerId={sellerId} />

          {/* Listings section */}
          <SellerProfileListings sellerId={sellerId} sellerProperties={profile.properties} />
        </div>
      </div>

      <Footer />
    </div>
  );
}
