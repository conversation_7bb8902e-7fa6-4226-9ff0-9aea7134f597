'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PropertyCard } from '@/components/PropertyCard';
import { Home } from 'lucide-react';
import {
  Property,
  PropertyType,
  TransactionType,
  PropertyStatus,
  Currency,
} from '@/lib/api/services/fetchProperty';
import { SellerProperty } from '@/lib/api/services/fetchUser';

interface SellerProfileListingsProps {
  sellerId: string;
  sellerProperties?: SellerProperty[];
}

// Mock data removed - now using real API data

function SellerProfileListings({
  sellerId: _sellerId,
  sellerProperties = [],
}: SellerProfileListingsProps) {
  const [activeTab, setActiveTab] = useState('all');
  const [showAll, setShowAll] = useState(false);

  // Reset showAll when switching tabs
  useEffect(() => {
    setShowAll(false);
  }, [activeTab]);

  // Transform SellerProperty to Property format for PropertyCard
  const transformedProperties: Property[] = sellerProperties.map(prop => ({
    id: prop.propertyId,
    saler: {
      id: _sellerId,
      fullName: 'Seller Name', // Will be filled from parent component
      phoneNumber: '',
      email: '',
      avatar: '',
    },
    title: prop.title,
    name: prop.title,
    description: prop.title,
    transactionType:
      prop.transactionType === 'ForSale' ? TransactionType.FOR_SALE : TransactionType.FOR_RENT,
    type: PropertyType.APARTMENT, // Default type
    status: PropertyStatus.AVAILABLE,
    code: '',
    location: {
      address: prop.address,
      latitude: 0,
      longitude: 0,
      city: '',
      district: '',
      ward: '',
    },
    propertyDetails: {
      bedrooms: prop.bedrooms,
      bathrooms: prop.bathrooms,
      livingRooms: 1,
      kitchens: 1,
      landArea: prop.landArea,
      buildingArea: prop.buildingArea,
      numberOfFloors: 1,
      hasBasement: false,
      floorNumber: 1,
    },
    priceDetails: {
      salePrice: prop.transactionType === 'ForSale' ? prop.price : 0,
      rentalPrice: prop.transactionType === 'ForRent' ? prop.price : 0,
      pricePerSquareMeter: prop.landArea > 0 ? prop.price / prop.landArea : 0,
      currency: Currency.VND,
      depositAmount: 0,
      maintenanceFee: 0,
      paymentMethods: [],
    },
    amenities: {
      parking: false,
      elevator: false,
      swimmingPool: false,
      gym: false,
      securitySystem: false,
      airConditioning: false,
      balcony: false,
      garden: false,
      playground: false,
      backupGenerator: false,
    },
    imageUrls: prop.images ? [prop.images] : [],
    floorPlanUrls: [],
    videoUrls: [],
    yearBuilt: 2020,
    legalDocumentUrls: [],
    isFeatured: false,
    isVerified: false,
    contactName: '',
    contactPhone: '',
    contactEmail: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isFavorite: false,
  }));

  const forSaleProperties = transformedProperties.filter(
    p => p.transactionType === TransactionType.FOR_SALE
  );
  const forRentProperties = transformedProperties.filter(
    p => p.transactionType === TransactionType.FOR_RENT
  );

  // Get current tab properties and displayed properties
  const getCurrentTabProperties = () => {
    switch (activeTab) {
      case 'sale':
        return forSaleProperties;
      case 'rent':
        return forRentProperties;
      default:
        return transformedProperties;
    }
  };

  const currentTabProperties = getCurrentTabProperties();
  const displayedProperties = showAll ? currentTabProperties : currentTabProperties.slice(0, 6);

  // Check if show more button should be displayed
  const shouldShowMoreButton = currentTabProperties.length > 6;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Home className="w-5 h-5" />
            <span>Bất động sản ({transformedProperties.length})</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">Tất cả ({transformedProperties.length})</TabsTrigger>
            <TabsTrigger value="sale">Bán ({forSaleProperties.length})</TabsTrigger>
            <TabsTrigger value="rent">Thuê ({forRentProperties.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-6">
            {currentTabProperties.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {displayedProperties.map(property => (
                  <PropertyCard key={property.id} property={property} priority={false} size="md" />
                ))}
              </div>
            ) : (
              <div className="col-span-full text-center py-8">
                <div className="text-gray-400 mb-2">
                  <Home className="w-12 h-12 mx-auto" />
                </div>
                <p className="text-gray-500">Chưa có bất động sản nào</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="sale" className="mt-6">
            {currentTabProperties.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {displayedProperties.map(property => (
                  <PropertyCard key={property.id} property={property} priority={false} size="md" />
                ))}
              </div>
            ) : (
              <div className="col-span-full text-center py-8">
                <div className="text-gray-400 mb-2">
                  <Home className="w-12 h-12 mx-auto" />
                </div>
                <p className="text-gray-500">Chưa có bất động sản bán</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="rent" className="mt-6">
            {currentTabProperties.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {displayedProperties.map(property => (
                  <PropertyCard key={property.id} property={property} priority={false} size="md" />
                ))}
              </div>
            ) : (
              <div className="col-span-full text-center py-8">
                <div className="text-gray-400 mb-2">
                  <Home className="w-12 h-12 mx-auto" />
                </div>
                <p className="text-gray-500">Chưa có bất động sản cho thuê</p>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Show More Button */}
        {shouldShowMoreButton && (
          <div className="text-center mt-8">
            <Button variant="outline" onClick={() => setShowAll(!showAll)} className="px-8">
              {showAll ? 'Ẩn bớt' : `Xem thêm ${currentTabProperties.length - 6} bất động sản`}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default SellerProfileListings;
