import React from 'react';
import { PropertyCard } from '@/components/PropertyCard';
import { TransactionType } from '@/lib/api/services/fetchProperty';
import { useProperties } from '@/hooks/useProperty';
import RecentlyViewedSection from '../RecentlyViewedSection';
import { Skeleton } from '@/components/ui/skeleton';

export default function BuyAbilitySection() {
  // Fetch newest properties for sale
  const { properties: saleProperties, isLoading: isLoadingSale } = useProperties({
    transactionType: TransactionType.FOR_SALE,
    pageSize: 8,
    pageNumber: 1,
    sortBy: 'createdAt',
    isDescending: true,
  });

  // Fetch newest properties for rent
  const { properties: rentProperties, isLoading: isLoadingRent } = useProperties({
    transactionType: TransactionType.FOR_RENT,
    pageSize: 8,
    pageNumber: 1,
    sortBy: 'createdAt',
    isDescending: true,
  });

  return (
    <section className="w-full flex flex-col items-center justify-center pt-12 pb-16 bg-white mt-0 md:mt-2">
      {/* Newest Properties for Sale Section */}
      <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36 mb-12">
        <div className="text-left mb-8">
          <h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-4">
            Bất động sản mới nhất - Bán
          </h2>
          <p className="text-sm md:text-base text-gray-600 max-w-4xl">
            Khám phá những bất động sản mới nhất dành cho việc mua bán được cập nhật liên tục.
          </p>
        </div>
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
          {isLoadingSale
            ? // Loading skeleton for sale properties
              Array.from({ length: 12 }).map((_, i) => (
                <div key={i} className="overflow-hidden transition-all duration-300 flex flex-col">
                  {/* Image Section with Badges */}
                  <div className="relative aspect-square size-full mb-2 group">
                    <Skeleton className="w-full h-full rounded-2xl" />
                    {/* Badge Skeletons */}
                    <div className="absolute top-4 left-4 flex gap-2">
                      <Skeleton className="h-6 w-12 rounded-full bg-white/20" />
                      <Skeleton className="h-6 w-16 rounded-full bg-white/20" />
                    </div>
                  </div>

                  <div className="px-1">
                    {/* Price and Actions Section */}
                    <div className="flex justify-between items-center">
                      <div>
                        <Skeleton className="h-6 w-24" />
                      </div>
                      <div className="flex gap-2">
                        <Skeleton className="h-8 w-8 rounded-md" />
                        <Skeleton className="h-8 w-8 rounded-md" />
                      </div>
                    </div>

                    {/* Property Details Section */}
                    <div className="flex justify-between items-center mb-2 mt-2">
                      <div className="flex gap-4">
                        <div className="flex items-center">
                          <Skeleton className="h-4 w-4 mr-1 rounded" />
                          <Skeleton className="h-4 w-3" />
                        </div>
                        <div className="flex items-center">
                          <Skeleton className="h-4 w-4 mr-1 rounded" />
                          <Skeleton className="h-4 w-3" />
                        </div>
                        <div className="flex items-center">
                          <Skeleton className="h-4 w-4 mr-1 rounded" />
                          <Skeleton className="h-4 w-8" />
                        </div>
                      </div>
                      <Skeleton className="h-3 w-16" />
                    </div>

                    {/* Location Section */}
                    <div className="mb-2">
                      <div className="flex items-center">
                        <Skeleton className="h-4 w-4 mr-1 rounded" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            : saleProperties.map(property => (
                <PropertyCard key={property.id} property={property} priority={true} size="md" />
              ))}
        </div>
      </div>

      {/* Newest Properties for Rent Section */}
      <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36 mb-12">
        <div className="text-left mb-8">
          <h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-4">
            Bất động sản mới nhất - Cho thuê
          </h2>
          <p className="text-sm md:text-base text-gray-600 max-w-4xl">
            Khám phá những bất động sản mới nhất dành cho việc cho thuê với đa dạng lựa chọn.
          </p>
        </div>
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
          {isLoadingRent
            ? // Loading skeleton for rent properties
              Array.from({ length: 8 }).map((_, index) => (
                <div key={`rent-skeleton-${index}`} className="animate-pulse">
                  <div className="bg-gray-200 rounded-lg h-64 mb-4"></div>
                  <div className="bg-gray-200 h-4 rounded mb-2"></div>
                  <div className="bg-gray-200 h-4 rounded w-3/4"></div>
                </div>
              ))
            : rentProperties.map(property => (
                <PropertyCard key={property.id} property={property} priority={false} size="md" />
              ))}
        </div>
      </div>

      {/* Recently Viewed Section */}
      <RecentlyViewedSection />
    </section>
  );
}
