'use client';

import { Card, CardContent } from '@/components/ui/card';
import AgentReviewList from '../../../properties/[id]/component/AgentReviewList';

interface SellerProfileReviewsWrapperProps {
  sellerId: string;
}

/**
 * Wrapper component that uses the existing AgentReviewList component
 * for displaying seller reviews in a Card layout
 */
export default function SellerProfileReviewsWrapper({
  sellerId,
}: SellerProfileReviewsWrapperProps) {
  return (
    <Card id="feedbacks" className="mb-4 md:mb-8 w-full">
      <CardContent className="space-y-4">
        <AgentReviewList agentId={sellerId} />
      </CardContent>
    </Card>
  );
}
