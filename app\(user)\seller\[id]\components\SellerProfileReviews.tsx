'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface Review {
  id: string;
  clientName: string;
  rating: number;
  date: string;
  comment: string;
}

interface SellerProfileReviewsProps {
  reviews: Review[];
  rating: number;
  reviewCount: number;
}

function SellerProfileReviews({ reviews, rating, reviewCount }: SellerProfileReviewsProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const reviewsPerPage = 6;
  const totalPages = Math.ceil(reviews.length / reviewsPerPage);

  const startIndex = (currentPage - 1) * reviewsPerPage;
  const endIndex = startIndex + reviewsPerPage;
  const currentReviews = reviews.slice(startIndex, endIndex);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'fill-yellow-500 text-yellow-500' : 'text-gray-300'
        }`}
      />
    ));
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getVisiblePages = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        pages.push(1, 2, 3, '...', totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1, '...', totalPages - 2, totalPages - 1, totalPages);
      } else {
        pages.push(1, '...', currentPage, '...', totalPages);
      }
    }

    return pages;
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <Card id="feedbacks" className="mb-4 md:mb-8 w-full">
      <CardContent className="space-y-4">
        <section id="feedback" className="space-y-8">
          {/* Tổng đánh giá */}
          <div className="space-y-1 mt-6">
            <div className="flex items-center gap-2 text-lg md:text-xl font-semibold">
              <Star className="size-4 md:size-5" />
              Đánh giá của khách hàng
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-700">
              <div className="flex items-center gap-1 text-yellow-500">
                {renderStars(Math.floor(rating))}
              </div>
              <span className="font-medium">
                ({rating.toFixed(1)}) – {reviewCount} đánh giá
              </span>
            </div>
          </div>

          {/* Danh sách feedback */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {currentReviews.map(review => (
              <div
                key={review.id}
                className="bg-gray-50 rounded-lg p-5 space-y-3 relative shadow-sm border border-gray-200 flex flex-col justify-between h-full"
              >
                {/* Nội dung đầu thẻ: tag + rating + content */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="bg-white text-gray-600 text-xs px-2 py-1 rounded-full border border-gray-300 font-semibold">
                      Đánh giá người bán
                    </span>
                    <div className="flex items-center gap-1 text-black font-medium">
                      <Star className="w-4 h-4 fill-black text-black" />
                      <span>{review.rating.toFixed(1)}</span>
                    </div>
                  </div>

                  <p className="text-sm text-gray-700 leading-relaxed">{review.comment}</p>
                </div>

                {/* Avatar */}
                <div className="flex justify-between items-center pt-4 mt-auto">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src="" alt={review.clientName} className="object-cover" />
                      <AvatarFallback className="bg-red-100 text-red-600 font-semibold text-xs">
                        {getInitials(review.clientName || 'Ẩn danh')}
                      </AvatarFallback>
                    </Avatar>
                    {/* Time */}
                    <div className="text-xs text-gray-500 leading-tight">
                      <p className="text-sm font-semibold text-gray-800">
                        {review.clientName ?? 'Ẩn danh'}
                      </p>
                      <p>{review.date || 'Không rõ thời gian'}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2 pt-6">
              {/* Previous Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="p-2"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              {/* Page Numbers */}
              {getVisiblePages().map((page, index) => (
                <div key={index}>
                  {page === '...' ? (
                    <span className="px-3 py-2 text-gray-500">...</span>
                  ) : (
                    <Button
                      variant={currentPage === page ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => handlePageChange(page as number)}
                      className={`w-8 h-8 p-0 ${
                        currentPage === page
                          ? 'bg-black text-white hover:bg-gray-800'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      {page}
                    </Button>
                  )}
                </div>
              ))}

              {/* Next Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="p-2"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          )}

          {/* Pagination Info */}
          {totalPages > 1 && (
            <div className="text-center text-sm text-gray-500">
              Showing {startIndex + 1} - {Math.min(endIndex, reviews.length)} of {reviews.length}
            </div>
          )}
        </section>
      </CardContent>
    </Card>
  );
}

export default SellerProfileReviews;
