'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User, Award, Target } from 'lucide-react';

interface SellerProfileAboutProps {
  about: string;
  specialties: string[];
  experience: string;
  joinedAt?: string;
  serviceLocations?: string | null;
}

function SellerProfileAbout({
  about,
  specialties,
  experience,
  joinedAt,
  serviceLocations,
}: SellerProfileAboutProps) {
  // Format joined date
  const formatJoinedDate = (dateString?: string) => {
    if (!dateString) return 'Chưa cập nhật';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };
  return (
    <div className="space-y-6">
      {/* About Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>Giới thiệu</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 leading-relaxed">{about}</p>
        </CardContent>
      </Card>

      {/* Specialties Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="w-5 h-5" />
            <span>Chuyên môn</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {specialties.map((specialty, index) => (
              <Badge key={index} variant="secondary" className="px-3 py-1">
                {specialty}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Experience Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="w-5 h-5" />
            <span>Kinh nghiệm</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h4 className="font-semibold text-gray-900">Kinh nghiệm làm việc</h4>
                <p className="text-gray-600">{experience} trong lĩnh vực bất động sản</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {experience.includes('năm') ? experience.split(' ')[0] : 'N/A'}
                </div>
                <div className="text-sm text-gray-500">Năm</div>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div>
                <h4 className="font-semibold text-gray-900">Ngày tham gia</h4>
                <p className="text-gray-600">Tham gia từ {formatJoinedDate(joinedAt)}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h5 className="font-semibold text-gray-900 mb-2">Chứng chỉ</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Chứng chỉ hành nghề môi giới BDS</li>
                  <li>• Chứng chỉ định giá BDS</li>
                  <li>• Chứng chỉ tư vấn đầu tư</li>
                </ul>
              </div>

              <div className="p-4 border rounded-lg">
                <h5 className="font-semibold text-gray-900 mb-2">Khu vực hoạt động</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  {serviceLocations ? (
                    <li>• {serviceLocations}</li>
                  ) : (
                    <>
                      <li>• TP. Hồ Chí Minh</li>
                      <li>• Bình Dương</li>
                      <li>• Đồng Nai</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default SellerProfileAbout;
