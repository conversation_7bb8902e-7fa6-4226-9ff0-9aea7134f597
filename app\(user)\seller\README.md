# Seller Profile Pages

Trang thông tin chi tiết của môi giới bất động sản.

## C<PERSON>u trúc thư mục

```
seller/
├── layout.tsx                 # Layout chung cho seller pages
├── [id]/                     # Dynamic route cho seller profile
│   ├── page.tsx              # Main page component
│   ├── loading.tsx           # Loading state
│   ├── not-found.tsx         # 404 page
│   └── components/           # Seller profile components
│       ├── SellerProfileClient.tsx      # Main client component
│       ├── SellerProfileHeader.tsx      # Header với cover image và avatar
│       ├── SellerProfileStats.tsx       # Statistics section
│       ├── SellerProfileAbout.tsx       # About section
│       ├── SellerProfileReviews.tsx     # Reviews section
│       ├── SellerProfileContact.tsx     # Contact sidebar
│       └── SellerProfileListings.tsx    # Property listings
└── README.md                 # Documentation
```

## Components

### SellerProfileClient

- Component chính quản lý state và layout
- Chứa mock data (sẽ được thay thế bằng API calls)
- Responsive layout với sidebar

### SellerProfileHeader

- Cover image với logo RevoLand
- Avatar với verified badge
- Thông tin cơ bản: tên, title, company
- Rating và số lượng reviews
- Action buttons

### SellerProfileStats

- 4 thống kê chính: Total Sales, Listings, Days on Market, Satisfaction
- Grid layout responsive
- Icons và colors phân biệt

### SellerProfileAbout

- Giới thiệu chi tiết
- Chuyên môn (specialties)
- Kinh nghiệm và chứng chỉ
- Khu vực hoạt động

### SellerProfileReviews

- Tổng quan rating với breakdown
- Danh sách reviews từ khách hàng
- Show more/less functionality
- Star rating display

### SellerProfileContact

- Sticky sidebar với thông tin liên hệ
- Avatar và thông tin cơ bản
- Action buttons: Call, Email, Message, Schedule
- Social media links
- Quick stats

### SellerProfileListings

- Tabs: All, For Sale, For Rent
- Sử dụng PropertyCard component có sẵn
- Show more functionality
- Grid layout responsive

## Usage

Truy cập trang seller profile qua URL: `/seller/[id]`

Ví dụ: `/seller/1`

## Integration với ContactSideBar

ContactSideBar đã được cập nhật để thêm link đến seller profile:

- Click vào avatar hoặc tên seller sẽ navigate đến `/seller/[id]`
- Hover effects để indicate clickable
- Không thay đổi functionality hiện tại

## TODO

- [ ] Tích hợp API calls thực tế
- [ ] Thêm error handling
- [ ] Thêm SEO metadata động
- [ ] Thêm social sharing
- [ ] Thêm contact form functionality
- [ ] Thêm real-time chat integration
- [ ] Thêm appointment booking
- [ ] Thêm property filtering trong listings
- [ ] Thêm pagination cho listings
- [ ] Thêm image optimization
