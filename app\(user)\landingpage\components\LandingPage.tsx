'use client';

import Image from 'next/image';
import React from 'react';
import BuyAbilitySection from './buyability/BuyAbilitySection';
import FeatureSection from './feature/FeatureSection';
import Footer from '@/components/Footer';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function LandingPage() {
  return (
    <div className="bg-white  ">
      {/* Hero Section - Full screen under header */}
      <div className="relative w-full h-[calc(100vh-3.5rem)]">
        <Image
          src="/bg_hero.jpg"
          alt="Landing Background"
          fill
          className="object-cover object-center brightness-90"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-black/30" />
        <div className="relative z-10 flex flex-col items-start justify-center h-full px-3 sm:px-6 lg:px-8 pb-8 sm:pb-10 md:pb-16">
          <div className="w-full max-w-sm sm:max-w-2xl mx-auto lg:mx-0 lg:ml-8 xl:ml-16 2xl:ml-64 flex flex-col gap-3 sm:gap-6">
            {/* Modern Pill Badge - Complex Staged Animation */}
            <div className="flex mb-2 sm:mb-4">
              <motion.div
                className="inline-flex items-center gap-1 sm:gap-2 pl-1 pr-2 sm:pr-3 py-1 rounded-full shadow-lg"
                initial={{ opacity: 0, width: 'auto' }}
                animate={{ opacity: 1, width: 'auto' }}
                transition={{ duration: 0.4, delay: 0.8, ease: 'easeOut' }}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(12px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                }}
              >
                {/* Inner Pill - Animates First */}
                <motion.div
                  className="flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-1 bg-black/70 backdrop-blur-sm rounded-full"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.5, ease: 'backOut' }}
                >
                  <motion.div
                    className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-400 rounded-full animate-pulse"
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.7, ease: 'easeOut' }}
                  />
                  <motion.span
                    className="text-white text-xs sm:text-base font-medium"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: 0.8, ease: 'easeOut' }}
                  >
                    Mới
                  </motion.span>
                </motion.div>

                {/* Text - Typewriter Effect */}
                <div className="text-white/90 text-xs sm:text-base font-normal min-w-[180px] sm:min-w-[280px] max-w-[200px] sm:max-w-none">
                  <span className="block sm:hidden">
                    {'Nền tảng công nghệ bất động sản.'.split('').map((char, index) => (
                      <motion.span
                        key={index}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{
                          duration: 0.05,
                          delay: 1.0 + index * 0.03,
                          ease: 'easeOut',
                        }}
                      >
                        {char}
                      </motion.span>
                    ))}
                  </span>
                  <span className="hidden sm:block">
                    {'Nền tảng công nghệ bất động sản toàn diện.'.split('').map((char, index) => (
                      <motion.span
                        key={index}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{
                          duration: 0.05,
                          delay: 1.0 + index * 0.03,
                          ease: 'easeOut',
                        }}
                      >
                        {char}
                      </motion.span>
                    ))}
                  </span>
                </div>
              </motion.div>
            </div>

            {/* Big Title Text - Animated First */}
            <motion.div
              className="font-sans text-white text-4xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-9xl font-medium drop-shadow-lg leading-tight sm:leading-relaxed text-center lg:text-left space-y-1 sm:space-y-0 mt-2 sm:mt-0"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
            >
              <div className="flex">Mua Bán.</div>
              <div className="flex">Cho Thuê.</div>
            </motion.div>

            {/* Description - Animated Second */}
            <motion.div
              className="text-white/90 text-sm sm:text-base md:text-lg lg:text-xl font-normal drop-shadow-md leading-relaxed text-left max-w-xs sm:max-w-xl sm:px-0"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: 'easeOut' }}
            >
              <span className="block sm:hidden">
                Revoland - Nền tảng công nghệ bất động sản toàn diện, an toàn và hiệu quả.
              </span>
              <span className="hidden sm:block">
                Revoland - Nền tảng công nghệ bất động sản toàn diện, mang đến giải pháp minh bạch,
                an toàn và hiệu quả cho mọi người.
              </span>
            </motion.div>

            {/* Action Button - Animated Third */}
            <motion.div
              className="flex mt-4 sm:mt-6"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
            >
              <Link
                href="/login"
                className="bg-red-600 hover:bg-red-600/80 text-white font-base px-6 sm:px-8 py-2.5 sm:py-3 text-xs sm:text-sm md:text-base rounded-full transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <span className="block sm:hidden">Bắt đầu</span>
                <span className="hidden sm:block">Bắt đầu ngay</span>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Content sections */}
      <section className="w-full flex flex-col lg:flex-row justify-center gap-2 sm:gap-4 lg:gap-6 mt-8 sm:mt-12 md:mt-16 lg:mt-20 py-6 sm:py-8 md:py-10 lg:py-12 bg-white px-2 sm:px-4 lg:px-6 xl:px-8">
        <div className="w-full max-w-[500px] flex flex-col items-center lg:items-start text-center lg:text-left px-2 sm:px-4 lg:px-0 mb-6 lg:mb-0 mx-auto">
          <h2 className="font-bold text-xl sm:text-2xl md:text-3xl lg:text-4xl mb-2 sm:mb-3 text-gray-900">
            Gợi ý nhà phù hợp
          </h2>
          <p className="text-muted-foreground text-sm sm:text-base md:text-lg mb-4 sm:mb-6 leading-relaxed">
            Đăng nhập để nhận trải nghiệm cá nhân hóa và các đề xuất tốt nhất cho bạn.
          </p>
          <button
            className="px-4 sm:px-6 py-2 sm:py-3 rounded-xl border border-red-500 text-red-500 font-semibold hover:bg-red-50 transition-colors text-sm sm:text-base"
            onClick={() => {
              window.location.href = '/login';
            }}
          >
            Đăng nhập
          </button>
        </div>
        <div className="w-full max-w-[500px] flex items-center justify-center mx-auto">
          <div className="relative w-[280px] sm:w-[320px] md:w-[360px] lg:w-[400px] xl:w-[440px]">
            <div className="relative rounded-2xl shadow-2xl bg-white overflow-visible pt-2 pb-4 px-3 sm:px-4">
              <div className="absolute -top-6 sm:-top-8 left-2 sm:left-4 flex items-center gap-2 bg-white rounded-full shadow-lg px-3 sm:px-4 py-2 z-10">
                <span className="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-green-500"></span>
                <div className="text-left">
                  <div className="font-semibold text-xs sm:text-sm text-gray-900 leading-tight">
                    Nhà đề xuất
                  </div>
                  <div className="text-xs text-gray-500">Dựa trên ngân sách của bạn</div>
                </div>
              </div>
              <div className="absolute -bottom-8 sm:-bottom-10 -right-10 sm:-right-12 lg:-right-16 flex items-center gap-2 bg-white rounded-full shadow-lg px-3 sm:px-4 py-2 z-10">
                <span className="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-orange-500"></span>
                <div className="text-left">
                  <div className="font-semibold text-xs sm:text-sm text-gray-900 leading-tight">
                    Nhà đề xuất
                  </div>
                  <div className="text-xs text-gray-500">Dựa trên vị trí ưu tiên</div>
                </div>
              </div>
              <Image
                src="/proper.jpg"
                width={600}
                height={128}
                alt="Recommended Home"
                className="w-full h-44 sm:h-56 md:h-64 object-cover rounded-xl mb-3 sm:mb-4"
              />
              <div className="font-bold text-lg sm:text-xl md:text-2xl text-gray-900 mb-2">
                Vinhome Grandpark
              </div>
              <div className="flex items-center text-gray-700 text-xs sm:text-sm gap-1 sm:gap-2 mb-2">
                <span>Tìm nhà hợp lí chỉ từ 3,xx tỷ - 5,xx tỷ</span>
              </div>
            </div>
          </div>
        </div>
      </section>
      <BuyAbilitySection />
      <FeatureSection />
      <Footer />
    </div>
  );
}
