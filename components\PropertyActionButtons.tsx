'use client';

import { useState, useEffect } from 'react';
import { Heart, Bookmark } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/lib/store/authStore';
import { useFavoriteProperty } from '@/hooks/useFavoriteProperty';
import { useCollections } from '@/hooks/useCollections';
import AddToCollectionModal from '@/components/collection/AddToCollectionModal';
import LoginRequiredModal from '@/components/collection/LoginRequiredModal';

interface PropertyActionButtonsProps {
  propertyId: string;
  propertyTitle: string;
  isFavorite?: boolean;
  variant?: 'card' | 'detail';
  size?: 'sm' | 'md';
  className?: string;
  onFavoriteChange?: (isFavorite: boolean) => void;
}

export default function PropertyActionButtons({
  propertyId,
  propertyTitle,
  isFavorite: initialIsFavorite = false,
  variant = 'detail',
  size = 'md',
  className,
  onFavoriteChange,
}: PropertyActionButtonsProps) {
  const { addFavorite, removeFavorite, isAddingFavorite, isRemovingFavorite } =
    useFavoriteProperty();
  const { isPropertyInCollections, removeFromAllCollections, isRemovingFromAllCollections } =
    useCollections();
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  const [isFavorite, setIsFavorite] = useState(initialIsFavorite);
  const [showAddToCollectionModal, setShowAddToCollectionModal] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);

  // Update local state when initialIsFavorite changes
  useEffect(() => {
    setIsFavorite(initialIsFavorite);
  }, [initialIsFavorite]);

  // Check if property is in any collection for bookmark styling
  const isInCollection = isPropertyInCollections(propertyId);

  const handleFavoriteClick = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!isAuthenticated) {
      setShowLoginModal(true);
      return;
    }

    if (isFavorite) {
      removeFavorite(propertyId);
      setIsFavorite(false);
      onFavoriteChange?.(false);
    } else {
      addFavorite({
        propertyId: propertyId,
        savedAt: new Date().toISOString(),
      });
      setIsFavorite(true);
      onFavoriteChange?.(true);
    }
  };

  const handleCollectionClick = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!isAuthenticated) {
      setShowLoginModal(true);
      return;
    }

    // If property is already in collection, remove from all collections
    if (isInCollection) {
      removeFromAllCollections(propertyId);
    } else {
      // If not in collection, show add to collection modal
      setShowAddToCollectionModal(true);
    }
  };

  // Styling based on variant
  const getButtonStyles = () => {
    // if (variant === 'card') {
    //   return {
    //     bookmarkButton: cn(
    //       'hover:text-yellow-600 transition-colors',
    //       isInCollection && 'text-yellow-600',
    //       isRemovingFromAllCollections && 'opacity-50 cursor-not-allowed'
    //     ),
    //     favoriteButton: cn(
    //       'hover:text-red-600 transition-colors',
    //       isFavorite && 'text-red-600',
    //       (isAddingFavorite || isRemovingFavorite) && 'opacity-50 cursor-not-allowed'
    //     ),
    //     bookmarkIcon: cn('size-5', isInCollection && 'fill-yellow-600'),
    //     favoriteIcon: cn('size-5', isFavorite && 'fill-current'),
    //   };
    // }

    // Detail variant styling
    return {
      bookmarkButton: cn(
        'transition-colors rounded-full',
        size === 'sm' ? 'size-9' : '',
        isInCollection &&
          'text-purple-600 hover:text-purple-700 bg-purple-100 hover:bg-purple-200 border-purple-100 hover:border-purple-200',
        isRemovingFromAllCollections && 'opacity-50 cursor-not-allowed'
      ),
      favoriteButton: cn(
        'transition-colors rounded-full',
        size === 'sm' ? 'size-9' : '',
        isFavorite &&
          'text-red-600 hover:text-red-700 bg-red-100 hover:bg-red-200 border-red-100 hover:border-red-200',
        (isAddingFavorite || isRemovingFavorite) && 'opacity-50 cursor-not-allowed'
      ),
      bookmarkIcon: cn(
        size === 'sm' ? 'h-3 w-3' : 'h-3 w-3 md:h-4 md:w-4',
        isInCollection && 'fill-purple-600'
      ),
      favoriteIcon: cn(
        size === 'sm' ? 'h-3 w-3' : 'h-3 w-3 md:h-4 md:w-4',
        isFavorite && 'fill-current'
      ),
    };
  };

  const styles = getButtonStyles();

  const BookmarkButton = () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={variant === 'card' ? 'ghost' : 'outline'}
          size="icon"
          className={styles.bookmarkButton}
          onClick={handleCollectionClick}
          disabled={isRemovingFromAllCollections}
        >
          <Bookmark className={styles.bookmarkIcon} />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Lưu bất động sản</p>
      </TooltipContent>
    </Tooltip>
  );

  const FavoriteButton = () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={variant === 'card' ? 'ghost' : 'outline'}
          size="icon"
          className={styles.favoriteButton}
          onClick={handleFavoriteClick}
          disabled={isAddingFavorite || isRemovingFavorite}
        >
          <Heart className={styles.favoriteIcon} />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Yêu thích</p>
      </TooltipContent>
    </Tooltip>
  );

  return (
    <>
      <div className={cn('flex gap-2', className)}>
        <BookmarkButton />
        <FavoriteButton />
      </div>

      {/* Modals */}
      <AddToCollectionModal
        isOpen={showAddToCollectionModal}
        onClose={() => setShowAddToCollectionModal(false)}
        propertyId={propertyId}
        _propertyTitle={propertyTitle}
      />
      <LoginRequiredModal isOpen={showLoginModal} onClose={() => setShowLoginModal(false)} />
    </>
  );
}
