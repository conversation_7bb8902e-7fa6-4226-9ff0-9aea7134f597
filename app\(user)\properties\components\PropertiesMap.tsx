'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { Property, TransactionType } from '@/lib/api/services/fetchProperty';

import {
  Map,
  AdvancedMarker,
  InfoWindow,
  useMap,
  useMapsLibrary,
  useAdvancedMarkerRef,
} from '@vis.gl/react-google-maps';
import { PropertyCard } from '@/components/PropertyCard';
import { Badge } from '@/components/ui/badge';

interface PropertiesMapProps {
  properties: Property[];
  center?: {
    lat: number;
    lng: number;
  };
  zoom?: number;
  onMapIdle?: (mapState: {
    bounds: google.maps.LatLngBounds;
    center: google.maps.LatLngLiteral;
    zoom: number;
  }) => void;
  hoveredPropertyId?: string | null;
  initialCenter?: { lat: number; lng: number };
  initialZoom?: number;
}

// Custom marker component that handles the price badge
function PropertyMarker({
  property,
  onClick,
  selectedProperty,
  isHovered,
}: {
  property: Property;
  onClick: () => void;
  selectedProperty: Property | null;
  isHovered: boolean;
}) {
  const map = useMap();
  const [zoomLevel, setZoomLevel] = useState<number>(map?.getZoom() ?? 13);
  const ZOOM_THRESHOLD = 16; // Switch to badge style at zoom level 14 and above

  useEffect(() => {
    if (!map) return;

    const zoomListener = map.addListener('zoom_changed', () => {
      const currentZoom = map.getZoom() ?? 13;
      setZoomLevel(currentZoom);
    });

    return () => {
      google.maps.event.removeListener(zoomListener);
    };
  }, [map]);

  function formatPriceShort(value: number): string {
    if (value >= 1_000_000_000) {
      return (value / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + 'B';
    }
    if (value >= 1_000_000) {
      return (value / 1_000_000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (value >= 1_000) {
      return (value / 1_000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return value.toString();
  }

  const rawPrice =
    property.transactionType === TransactionType.FOR_SALE
      ? property.priceDetails.salePrice || 0
      : property.priceDetails.rentalPrice || 0;

  const [markerRef] = useAdvancedMarkerRef();

  // Determine marker color based on transaction type and selection state
  const getMarkerColor = () => {
    if (selectedProperty?.id === property.id || isHovered) {
      return 'bg-green-600';
    }
    return property.transactionType === TransactionType.FOR_RENT ? 'bg-purple-600' : 'bg-red-600';
  };

  return (
    <AdvancedMarker
      ref={markerRef}
      position={{
        lat: property.location.latitude,
        lng: property.location.longitude,
      }}
      onClick={onClick}
    >
      {zoomLevel >= ZOOM_THRESHOLD ? (
        <Badge
          variant="default"
          className={`absolute -right-6 hover:bg-green-600   transition-colors duration-200 ${getMarkerColor()}`}
        >
          {formatPriceShort(rawPrice)}
        </Badge>
      ) : (
        <div
          className={`absolute w-3 h-3 rounded-full hover:bg-green-600 ${getMarkerColor()} transition-colors duration-200`}
          style={{
            transform: 'translate(-50%, -50%)',
            boxShadow: '0 0 0 2px white, 0 0 0 4px rgba(0,0,0,0.1)',
          }}
        />
      )}
    </AdvancedMarker>
  );
}

// Custom info window component that displays property details
function PropertyInfoWindow({ property, onClose }: { property: Property; onClose: () => void }) {
  const [isMediumScreen, setIsMediumScreen] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMediumScreen(window.innerWidth >= 768); // 768px is the md breakpoint in Tailwind
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <InfoWindow
      shouldFocus={true}
      headerDisabled
      onCloseClick={onClose}
      position={{
        lat: property.location.latitude,
        lng: property.location.longitude,
      }}
      minWidth={200}
      maxWidth={400}
      className="font-mann"
    >
      <PropertyCard property={property} size={isMediumScreen ? 'md' : 'sm'} />
    </InfoWindow>
  );
}

// Main map component
function MapContent({
  properties,
  // center,
  // zoom,
  onMapIdle,
  hoveredPropertyId,
}: PropertiesMapProps) {
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const map = useMap();
  const mapsLibrary = useMapsLibrary('maps');
  const hasInteracted = useRef(false);

  useEffect(() => {
    if (map && mapsLibrary) {
      map.setOptions({
        mapId: process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID,
      });

      // Set a flag on first user interaction.
      const interactionListeners = ['dragstart', 'zoom_changed'].map(eventName =>
        map.addListener(eventName, () => {
          if (!hasInteracted.current) {
            hasInteracted.current = true;
          }
        })
      );

      // Add idle listener
      const idleListener = map.addListener('idle', () => {
        // Only call onMapIdle after the first interaction
        if (!hasInteracted.current) {
          return;
        }

        const bounds = map.getBounds();
        const center = map.getCenter();
        const zoom = map.getZoom();

        if (bounds && center && zoom && onMapIdle) {
          onMapIdle({ bounds, center: center.toJSON(), zoom });
        }
      });

      return () => {
        google.maps.event.removeListener(idleListener);
        interactionListeners.forEach(listener => google.maps.event.removeListener(listener));
      };
    }
  }, [map, mapsLibrary, onMapIdle]);

  const handleMarkerClick = useCallback((property: Property) => {
    setSelectedProperty(prevProperty => (prevProperty?.id === property.id ? null : property));
  }, []);

  const handleInfoWindowClose = useCallback(() => {
    setSelectedProperty(null);
  }, []);

  return (
    <>
      {properties.map(property => (
        <PropertyMarker
          key={property.id}
          property={property}
          onClick={() => handleMarkerClick(property)}
          selectedProperty={selectedProperty}
          isHovered={property.id === hoveredPropertyId}
        />
      ))}
      {selectedProperty && (
        <PropertyInfoWindow property={selectedProperty} onClose={handleInfoWindowClose} />
      )}
    </>
  );
}

// Main component
export default function PropertiesMap(props: PropertiesMapProps) {
  const defaultCenter = props.center || {
    lat: 10.842935416604869,
    lng: 106.84182012230411,
  };

  return (
    <div className="w-full h-full overflow-hidden relative">
      <Map
        defaultCenter={props.initialCenter || defaultCenter}
        defaultZoom={props.initialZoom || 14}
        mapId={process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID}
        mapTypeControl={true}
        fullscreenControl={true}
        streetViewControl={true}
        className="w-full h-full"
      >
        <MapContent {...props} />
      </Map>
    </div>
  );
}
