import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/lib/store/authStore';
import userService, {
  User,
  UserResponse,
  UserUpdateResponse,
  GetSellerResponse,
  SellerProfileResponse,
} from '@/lib/api/services/fetchUser';
import { toast } from 'sonner';

/**
 * Hook to fetch current user's profile
 */
export function useUserProfile() {
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  return useQuery({
    queryKey: ['users', 'profile'],
    queryFn: () => userService.getUserProfile(),
    enabled: isAuthenticated,
    select: (data: UserResponse) => ({
      profile: data.data,
      status: data.status,
      message: data.message,
    }),
    retry: (failureCount, error: unknown) => {
      // Don't retry on 401 errors
      if (error && typeof error === 'object' && 'status' in error && error.status === 401) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}

/**
 * Hook to update user profile
 */
export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (profileData: Partial<User> | FormData) =>
      userService.updateUserProfile(profileData),
    onSuccess: (data: UserUpdateResponse) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['users', 'profile'] });
      }
      toast.success(data.message);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useSeller() {
  return useQuery({
    queryKey: ['users', 'sellers'],
    queryFn: () => userService.getSellerProfile(),
    select: (response: GetSellerResponse) => ({
      sellers: response.data.data,
      status: response.status,
      message: response.message,
      totalCount: response.data.totalCount,
      totalPages: response.data.totalPages,
      currentPage: response.data.currentPage,
      pageSize: response.data.pageSize,
    }),
  });
}

/**
 * Hook to fetch seller profile by ID
 */
export function useSellerProfile(sellerId: string) {
  return useQuery({
    queryKey: ['users', 'seller-profile', sellerId],
    queryFn: () => userService.getSellerProfileById(sellerId),
    enabled: !!sellerId,
    select: (response: SellerProfileResponse) => ({
      profile: response.data,
      status: response.status,
      message: response.message,
    }),
    retry: (failureCount, error: unknown) => {
      // Don't retry on 404 errors (seller not found)
      if (error && typeof error === 'object' && 'status' in error && error.status === 404) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}
