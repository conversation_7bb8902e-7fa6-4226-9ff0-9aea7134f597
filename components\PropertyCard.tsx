import Image from 'next/image';
import { Property, TransactionType, PropertyType } from '@/lib/api/services/fetchProperty';

import { Badge } from '@/components/ui/badge';
import {
  MapPin,
  Bed,
  Bath,
  Maximize,
  ChevronLeft,
  ChevronRight,
  Eye,
  PlusIcon,
  CheckIcon,
  History,
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

import { useRecentlyViewed } from '@/hooks/useRecentlyViewed';
import { formatViewedTimeDetailed } from '@/utils/dates/formatViewedTime';
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import PropertyActionButtons from './PropertyActionButtons';
import ShareButton from './ShareButton';

interface PropertyCardProps {
  property: Property;
  priority?: boolean;
  onHover?: (propertyId: string | null) => void;
  size?: 'sm' | 'md';
  viewedTime?: string;
  onCompareClick?: (property: Property) => void;
  selectedComparisonIds?: string[];
}

const getPropertyTypeText = (type: PropertyType, transactionType: TransactionType) => {
  const typeMap = {
    [PropertyType.APARTMENT]: 'Chung cư',
    [PropertyType.LAND_PLOT]: 'Nhà Đất',
    [PropertyType.VILLA]: 'Biệt thự',
    [PropertyType.SHOP_HOUSE]: 'Nhà phố',
    [PropertyType.MINI_SERVICE_APARTMENT]: 'Chung cư mini',
    [PropertyType.MOTEL]: 'Nhà nghỉ',
    [PropertyType.AIRBNB]: 'Nhà nghỉ',
    [PropertyType.HOUSE]: 'Nhà riêng',
    [PropertyType.TOWNHOUSE]: 'Nhà liền kề',
    [PropertyType.PROJECT_LAND]: 'Đất',
    [PropertyType.OFFICE]: 'Văn phòng',
    [PropertyType.WAREHOUSE]: 'Kho',
    [PropertyType.FACTORY]: 'Nhà máy',
    [PropertyType.INDUSTRIAL]: 'Nhà máy',
    [PropertyType.HOTEL]: 'Khách sạn',
    [PropertyType.SOCIAL_HOUSING]: 'Nhà ở xã hội',
    [PropertyType.NEW_URBAN_AREA]: 'Khu đô thị mới',
    [PropertyType.ECO_RESORT]: 'Khu resort',
    [PropertyType.OTHER]: 'Khác',
  };

  const transactionText = transactionType === TransactionType.FOR_SALE ? 'bán' : 'cho thuê';
  return `${typeMap[type as keyof typeof typeMap]} ${transactionText}`;
};

const getPropertyType = (type: PropertyType) => {
  const typeMap = {
    [PropertyType.APARTMENT]: 'Chung cư',
    [PropertyType.LAND_PLOT]: 'Nhà Đất',
    [PropertyType.VILLA]: 'Biệt thự',
    [PropertyType.SHOP_HOUSE]: 'Nhà phố',
    [PropertyType.MINI_SERVICE_APARTMENT]: 'Chung cư mini',
    [PropertyType.MOTEL]: 'Nhà nghỉ',
    [PropertyType.AIRBNB]: 'Nhà nghỉ',
    [PropertyType.HOUSE]: 'Nhà riêng',
    [PropertyType.TOWNHOUSE]: 'Nhà liền kề',
    [PropertyType.PROJECT_LAND]: 'Đất',
    [PropertyType.OFFICE]: 'Văn phòng',
    [PropertyType.WAREHOUSE]: 'Kho',
    [PropertyType.FACTORY]: 'Nhà máy',
    [PropertyType.INDUSTRIAL]: 'Nhà máy',
    [PropertyType.HOTEL]: 'Khách sạn',
    [PropertyType.SOCIAL_HOUSING]: 'Nhà ở xã hội',
    [PropertyType.NEW_URBAN_AREA]: 'Khu đô thị mới',
    [PropertyType.ECO_RESORT]: 'Khu resort',
    [PropertyType.OTHER]: 'Khác',
  };

  return `${typeMap[type as keyof typeof typeMap]}`;
};

const formatPrice = (price: number, transactionType: TransactionType) => {
  if (transactionType === TransactionType.FOR_RENT) {
    if (price >= 1000000) {
      return (
        <span>
          {(price / 1000000).toFixed(1)} triệu
          <span className="text-sm text-muted-foreground font-light"> /tháng</span>
        </span>
      );
    }
    return (
      <span>
        {(price / 1000).toFixed(1)} nghìn
        <span className="text-sm text-muted-foreground font-light"> /tháng</span>
      </span>
    );
  } else {
    if (price >= 1000000000) {
      return <span>{(price / 1000000000).toFixed(1)} tỷ</span>;
    } else if (price >= 1000000) {
      return <span>{(price / 1000000).toFixed(1)} triệu</span>;
    } else {
      return <span>{(price / 1000).toFixed(1)} nghìn</span>;
    }
  }
};

export function PropertyCard({
  property,
  priority = false,
  onHover,
  size = 'md',
  viewedTime,
  onCompareClick,
  selectedComparisonIds,
}: PropertyCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [preloadedImages, setPreloadedImages] = useState<number[]>([0]);
  const [isImageLoading, setIsImageLoading] = useState(true);

  // Check if property is in recently viewed
  const { allRecentlyViewedWithTime } = useRecentlyViewed();
  const recentlyViewedItem = allRecentlyViewedWithTime.find(
    item => item.property.id === property.id
  );
  const isRecentlyViewed = viewedTime ? true : !!recentlyViewedItem;
  const displayViewedTime = viewedTime || recentlyViewedItem?.viewedAt;

  // Handle hover state changes
  const handleMouseEnter = () => {
    setIsHovered(true);
    onHover?.(property.id);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    onHover?.(null);
  };

  // Preload next and previous images
  useEffect(() => {
    const imageUrls = property.imageUrls || [];
    const preloadImage = (index: number) => {
      if (index >= 0 && index < imageUrls.length && !preloadedImages.includes(index)) {
        const img = new window.Image();
        img.src = imageUrls[index];
        setPreloadedImages(prev => [...prev, index]);
      }
    };

    // Preload next image
    const nextIndex = (currentImageIndex + 1) % imageUrls.length;
    preloadImage(nextIndex);

    // Preload previous image
    const prevIndex = currentImageIndex === 0 ? imageUrls.length - 1 : currentImageIndex - 1;
    preloadImage(prevIndex);
  }, [currentImageIndex, property.imageUrls, preloadedImages]);

  const nextImage = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsImageLoading(true);
    setCurrentImageIndex(prev => (prev === property.imageUrls.length - 1 ? 0 : prev + 1));
  };

  const prevImage = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsImageLoading(true);
    setCurrentImageIndex(prev => (prev === 0 ? property.imageUrls.length - 1 : prev - 1));
  };

  const isCompared = selectedComparisonIds?.includes(property.id);

  return (
    <TooltipProvider>
      <Link target="_blank" href={`/properties/${property.id}`}>
        <div
          className="overflow-hidden transition-all duration-300 flex flex-col"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {/* Image Section with Badge */}
          <div className="relative aspect-square size-full mb-2 group">
            {/* Skeleton Loading */}
            {isImageLoading && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-xl" />
            )}

            {/* Main Image */}
            <Image
              src={property.imageUrls[currentImageIndex] || '/placeholder-property.jpg'}
              alt={property.title}
              fill
              priority={priority}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className={cn(
                'object-cover rounded-2xl transition-opacity duration-300',
                isImageLoading ? 'opacity-0' : 'opacity-100'
              )}
              onLoad={() => setIsImageLoading(false)}
            />

            {/* Preload Hidden Images */}
            {property.imageUrls.map(
              (url, index) =>
                index !== currentImageIndex && (
                  <Image
                    key={index}
                    src={url}
                    alt={`${property.title} - Image ${index + 1}`}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="hidden"
                    priority={index === 0}
                  />
                )
            )}
            <div className="absolute w-full top-4 px-4 flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Badge
                  variant="outline"
                  className={cn(
                    ' bg-white/90 backdrop-blur-sm hover:bg-white/90 z-10',
                    size === 'sm' && 'text-[10px] px-2 py-0.5'
                  )}
                >
                  {property.transactionType === TransactionType.FOR_SALE ? 'Bán' : 'Cho thuê'}
                </Badge>
                <Badge
                  variant="outline"
                  className={cn(
                    ' bg-white/90 backdrop-blur-sm hover:bg-white/90 z-10',
                    size === 'sm' && 'text-[10px] px-2 py-0.5'
                  )}
                >
                  {getPropertyType(property.type)}
                </Badge>
              </div>
              {onCompareClick && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className={cn(
                        'z-10 relative bg-white/90 backdrop-blur-sm hover:bg-white/90 rounded-full'
                      )}
                      onClick={e => {
                        e.preventDefault(); // Prevent link navigation
                        onCompareClick?.(property);
                      }}
                    >
                      <span
                        className={cn(
                          'transition-all duration-200',
                          isCompared ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
                        )}
                      >
                        <CheckIcon className="stroke-green-600 dark:stroke-green-400 size-3" />
                      </span>
                      <span
                        className={cn(
                          'absolute transition-all duration-200',
                          isCompared ? 'scale-0 opacity-0' : 'scale-100 opacity-100'
                        )}
                      >
                        <PlusIcon />
                      </span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent className="px-2 py-1 text-xs">So sánh</TooltipContent>
                </Tooltip>
              )}
            </div>
            {/* Navigation Buttons */}
            {isHovered && property.imageUrls.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 rounded-full h-8 w-8 z-10"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-5 w-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 rounded-full h-8 w-8 z-10"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-5 w-5" />
                </Button>
              </>
            )}

            {/* Recently Viewed Eye Icon */}
            {isRecentlyViewed && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="absolute bottom-4 left-4 backdrop-blur-sm z-10 gap-1 inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-primary text-primary-foreground hover:bg-primary/80">
                    <Eye className={cn('text-white', size === 'sm' ? 'size-3' : 'size-4')} />
                    <span
                      className={cn(
                        'text-white text-xs font-medium',
                        size === 'sm' && 'text-[10px]'
                      )}
                    >
                      Đã xem
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent className="px-2 py-1 text-xs">
                  <div className="flex items-center gap-1.5">
                    <History className={cn('', size === 'sm' ? 'size-3' : 'size-4')} />
                    {displayViewedTime && formatViewedTimeDetailed(displayViewedTime)}
                  </div>
                </TooltipContent>
              </Tooltip>
            )}

            {/* Image Counter */}
            {property.imageUrls.length > 1 && (
              <div
                className={cn(
                  'absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full z-10',
                  size === 'sm' && 'text-[10px]'
                )}
              >
                {currentImageIndex + 1}/{property.imageUrls.length}
              </div>
            )}
          </div>

          <div className="px-1">
            {/* Price and Actions Section */}
            <div className="flex justify-between items-center">
              <div>
                <p className={cn('text-xl font-semibold', size === 'sm' && 'text-base')}>
                  {formatPrice(
                    property.transactionType === TransactionType.FOR_SALE
                      ? property.priceDetails.salePrice || 0
                      : property.priceDetails.rentalPrice || 0,
                    property.transactionType
                  )}
                </p>
              </div>
              <div className="flex gap-2">
                <PropertyActionButtons
                  propertyId={property.id}
                  propertyTitle={property.title}
                  isFavorite={property.isFavorite}
                  variant="card"
                  size="sm"
                />
                <ShareButton property={property} variant="card" size="sm" />
              </div>
            </div>

            {/* Property Details Section */}
            <div
              className={cn(
                'flex justify-between items-center mb-2 text-foreground text-sm',
                size === 'sm' && 'text-xs'
              )}
            >
              <div className="flex gap-4">
                <div className="flex items-center">
                  <Bed className={cn('size-4 mr-1', size === 'sm' && 'size-3')} />
                  <span>{property.propertyDetails.bedrooms}</span>
                </div>
                <div className="flex items-center">
                  <Bath className={cn('size-4 mr-1', size === 'sm' && 'size-3')} />
                  <span>{property.propertyDetails.bathrooms}</span>
                </div>
                <div className="flex items-center">
                  <Maximize className={cn('size-4 mr-1', size === 'sm' && 'size-3')} />
                  <span>{property.propertyDetails.buildingArea} m²</span>
                </div>
              </div>
              <div className={cn('text-xs text-muted-foreground', size === 'sm' && 'text-xs')}>
                {getPropertyTypeText(property.type, property.transactionType)}
              </div>
            </div>

            {/* Location Section */}
            <div className="mb-2">
              <div
                className={cn(
                  'flex items-center text-xs text-muted-foreground',
                  size === 'sm' && 'text-[10px]'
                )}
              >
                <MapPin className={cn('size-4 mr-1', size === 'sm' && 'size-3')} />
                <span>
                  {property.location?.city || 'Location unavailable'}, {property.location?.district}
                </span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </TooltipProvider>
  );
}
