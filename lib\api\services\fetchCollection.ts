import apiService from '@/lib/api/core';
import { Property } from './fetchProperty';

export interface Collection {
  id: string;
  name: string;
  description?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  itemCount: number;
  thumbnailUrl?: string;
  collectionImage?: string[];
  // Add lazy loading flag
  isImageLoaded?: boolean;
}

export interface CollectionItem {
  id: string;
  collectionId: string;
  propertyId: string;
  addedAt: string;
}

export interface CollectionWithItems extends Collection {
  items: CollectionItem[];
  properties?: Property[];
}

export interface ApiCollectionData {
  collectionId: string;
  collectionName: string;
  description: string;
  userId: string;
  collectionImage: string[];
  createAt: string;
  updateAt: string;
  listProperties?: Property[]; // Optional for backward compatibility
  numberOfProperties: number;
}
export interface ApiProperty {
  data: Property[];
}
export interface CreateCollectionRequest {
  name: string;
  description?: string;
}

export interface AddToCollectionRequest {
  collectionId: string;
  propertyId: string;
}

export interface RemoveFromCollectionRequest {
  collectionId: string;
  propertyId: string;
}

export interface UpdateCollectionRequest {
  name: string;
  description: string;
}

export interface CollectionResponse {
  code: number;
  status: boolean;
  message: string;
  data: Collection | null;
}

export interface CollectionsResponse {
  code: number;
  status: boolean;
  message: string;
  data: Collection[];
  pagination?: {
    totalCount: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  };
}

export interface ApiCollectionsResponse {
  code: number;
  status: boolean;
  message: string;
  data: ApiCollection;
}
export interface ApiCollection {
  data: ApiCollectionData[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}
export interface ActionResponse {
  code: number;
  status: boolean;
  message: string;
  data?: unknown;
}

// Interface for single collection API response
export interface ApiCollectionDetailResponse {
  code: number;
  status: boolean;
  message: string;
  data: ApiCollectionDetailData;
}

// Interface for single collection detail data
export interface ApiCollectionDetailData {
  collectionId: string;
  collectionName: string;
  description: string;
  userId: string;
  properties: {
    data: Property[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  };
  collectionImage: string[];
  createdAt: string;
  updatedAt: string;
}

// Extract valid images from properties
// eslint-disable-next-line @typescript-eslint/no-explicit-any
// const extractValidImages = (properties: any[], maxImages: number = 4): string[] => {
//   const validImages: string[] = [];

//   for (const property of properties.slice(0, maxImages)) {
//     if (validImages.length >= maxImages) break;

//     const propertyImages = property.images || property.imageUrls || [];
//     if (propertyImages?.length > 0) {
//       const firstImage = propertyImages[0];
//       if (
//         firstImage &&
//         firstImage.trim() !== '' &&
//         !firstImage.includes('undefined') &&
//         firstImage.includes('storage.googleapis.com')
//       ) {
//         validImages.push(firstImage);
//       }
//     }
//   }

//   return validImages;
// };

// Extract collection images with validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
// const extractCollectionImages = (properties: any[], existingImages: string[] = []): string[] => {
//   // Validate existing images first
//   const validExistingImages = (existingImages || [])
//     .filter(
//       img =>
//         img &&
//         img.trim() !== '' &&
//         !img.includes('undefined') &&
//         img.includes('storage.googleapis.com')
//     )
//     .slice(0, 4);

//   if (validExistingImages.length > 0) {
//     return validExistingImages;
//   }

//   // Extract from properties if no existing images
//   return extractValidImages(properties || [], 4);
// };

// Transform API data to Collection format (for collections list)
const transformApiCollectionToCollection = (apiData: ApiCollectionData): Collection => {
  // For the new API format, we get collection images directly
  const collectionImages = (apiData.collectionImage || [])
    .filter(img => img && img.trim() !== '' && img.includes('storage.googleapis.com'))
    .slice(0, 4);

  return {
    id: apiData.collectionId,
    name: apiData.collectionName,
    description: apiData.description || undefined,
    userId: apiData.userId,
    itemCount: apiData.numberOfProperties,
    thumbnailUrl: collectionImages[0] || undefined,
    collectionImage: collectionImages,
    createdAt: apiData.createAt,
    updatedAt: apiData.updateAt,
    isImageLoaded: false,
  };
};

// Transform collection detail API data to Collection format
const transformApiCollectionDetailToCollection = (apiData: ApiCollectionDetailData): Collection => {
  const collectionImages = (apiData.collectionImage || [])
    .filter(img => img && img.trim() !== '' && img.includes('storage.googleapis.com'))
    .slice(0, 4);

  return {
    id: apiData.collectionId,
    name: apiData.collectionName,
    description: apiData.description || undefined,
    userId: apiData.userId,
    itemCount: apiData.properties?.data?.length || 0,
    thumbnailUrl: collectionImages[0] || undefined,
    collectionImage: collectionImages,
    createdAt: apiData.createdAt,
    updatedAt: apiData.updatedAt,
    isImageLoaded: false,
  };
};

// Request debouncing to prevent rapid API calls
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 200;

const debounceRequest = async <T>(requestFn: () => Promise<T>): Promise<T> => {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    await new Promise(resolve => setTimeout(resolve, MIN_REQUEST_INTERVAL - timeSinceLastRequest));
  }

  lastRequestTime = Date.now();
  return requestFn();
};

// Collection Service
export const collectionService = {
  // GET: Get all collections
  getAllByUser: async (): Promise<CollectionsResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.get<ApiCollectionsResponse>('/api/collection-properties/mine');
      });

      if (!response.data.status) {
        return {
          code: response.data.code,
          status: false,
          message: response.data.message || 'Không thể tải bộ sưu tập',
          data: [],
          pagination: {
            totalCount: 0,
            totalPages: 0,
            currentPage: 1,
            pageSize: 10,
          },
        };
      }

      const collections = (response.data.data.data || []).map(transformApiCollectionToCollection);

      return {
        code: response.data.code,
        status: response.data.status,
        message: response.data.message,
        data: collections,
        pagination: {
          totalCount: response.data.data.totalCount,
          totalPages: response.data.data.totalPages,
          currentPage: response.data.data.currentPage,
          pageSize: response.data.data.pageSize,
        },
      };
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi tải bộ sưu tập',
        data: [],
        pagination: {
          totalCount: 0,
          totalPages: 0,
          currentPage: 1,
          pageSize: 10,
        },
      };
    }
  },

  // GET: Get specific collection by ID
  getCollectionById: async (collectionId: string): Promise<CollectionResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.get<ApiCollectionDetailResponse>(
          `/api/collection-properties/${collectionId}`
        );
      });

      if (!response.data.status) {
        return {
          code: response.data.code,
          status: false,
          message: response.data.message || 'Không thể tải bộ sưu tập',
          data: null,
        };
      }

      const collection = transformApiCollectionDetailToCollection(response.data.data);

      return {
        code: response.data.code,
        status: response.data.status,
        message: response.data.message,
        data: collection,
      };
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi tải bộ sưu tập',
        data: null,
      };
    }
  },

  // POST: Tạo collection mới
  createCollection: async (request: CreateCollectionRequest): Promise<CollectionResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.post<ActionResponse>(
          '/api/collection-properties',
          request as unknown as Record<string, unknown>
        );
      });

      return {
        code: response.data.code,
        status: response.data.status,
        message: response.data.message,
        data: null,
      };
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi tạo bộ sưu tập',
        data: null,
      };
    }
  },

  // POST: Thêm property vào collection
  addPropertyToCollection: async (request: AddToCollectionRequest): Promise<ActionResponse> => {
    try {
      const { collectionId, propertyId } = request;
      const response = await debounceRequest(async () => {
        return apiService.post<ActionResponse>(
          `/api/collection-properties/add-property?collectionId=${collectionId}&propertyId=${propertyId}`
        );
      });

      return response.data;
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi thêm vào bộ sưu tập',
      };
    }
  },

  // DELETE: Xóa property khỏi collection
  removePropertyFromCollection: async (
    request: RemoveFromCollectionRequest
  ): Promise<ActionResponse> => {
    try {
      const { collectionId, propertyId } = request;
      const response = await debounceRequest(async () => {
        return apiService.delete<ActionResponse>(
          `/api/collection-properties/remove-property?collectionId=${collectionId}&property=${propertyId}`
        );
      });

      return response.data;
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi xóa khỏi bộ sưu tập',
      };
    }
  },

  // DELETE: Xóa collection
  deleteCollection: async (collectionId: string): Promise<ActionResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.delete<ActionResponse>(`/api/collection-properties/${collectionId}`);
      });

      return response.data;
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi xóa bộ sưu tập',
      };
    }
  },

  // PUT: Cập nhật collection
  updateCollection: async (
    collectionId: string,
    request: UpdateCollectionRequest
  ): Promise<ActionResponse> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.put<ActionResponse>(
          `/api/collection-properties/${collectionId}`,
          request as unknown as Record<string, unknown>
        );
      });

      return response.data;
    } catch (error) {
      return {
        code: 500,
        status: false,
        message: 'Có lỗi xảy ra khi cập nhật bộ sưu tập',
      };
    }
  },

  // GET: Lấy chi tiết collection với lazy loading
  getCollectionWithProperties: async (
    collectionId: string
  ): Promise<CollectionWithItems | null> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.get<ApiCollectionsResponse>('/api/collection-properties');
      });

      if (!response.data.status) {
        return null;
      }

      const apiCollection = response.data.data.data?.find(col => col.collectionId === collectionId);
      if (!apiCollection) return null;

      const collection = transformApiCollectionToCollection(apiCollection);

      return {
        ...collection,
        items: (apiCollection.listProperties || []).map((prop: Property) => ({
          id: `${collectionId}-${prop.id}`,
          collectionId,
          propertyId: prop.id,
          addedAt: new Date().toISOString(),
        })),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        properties: (apiCollection.listProperties || []).map((prop: Property) => ({
          ...prop,
          imageUrls: prop.imageUrls || [], // Use images from API, fallback to imageUrls
        })),
      };
    } catch (error) {
      return null;
    }
  },

  // GET: Get collection with properties using specific collection ID endpoint
  getCollectionWithPropertiesById: async (
    collectionId: string
  ): Promise<CollectionWithItems | null> => {
    try {
      const response = await debounceRequest(async () => {
        return apiService.get<ApiCollectionDetailResponse>(
          `/api/collection-properties/${collectionId}`
        );
      });

      if (!response.data.status) {
        return null;
      }

      const apiCollection = response.data.data;
      const collection = transformApiCollectionDetailToCollection(apiCollection);

      return {
        ...collection,
        items: (apiCollection.properties.data || []).map((prop: Property) => ({
          id: `${collectionId}-${prop.id}`,
          collectionId,
          propertyId: prop.id,
          addedAt: new Date().toISOString(),
        })),
        properties: (apiCollection.properties.data || []).map((prop: Property) => ({
          ...prop,
          imageUrls: prop.imageUrls || [], // Use images from API, fallback to imageUrls
        })),
      };
    } catch (error) {
      return null;
    }
  },
};
